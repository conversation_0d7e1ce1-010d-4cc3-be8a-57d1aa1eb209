# Manticore Search 高内聚模块

基于 BMAD 工程规范的 Manticore Search 模块，提供完整的搜索引擎功能和 FastAPI 接口。

## 🏗️ 架构设计

### 模块结构

```
manticore_search/
├── __init__.py              # 模块入口
├── api/                     # FastAPI 接口层
│   ├── __init__.py
│   └── main.py             # 主应用和路由
├── clients/                 # 数据访问层
│   ├── __init__.py
│   └── manticore_client.py # Manticore 客户端
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── document_service.py # 文档管理服务
│   ├── search_service.py   # 搜索服务
│   └── health_service.py   # 健康检查服务
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── document.py         # 文档模型
│   ├── search.py          # 搜索模型
│   └── response.py        # 响应模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志管理
│   └── exceptions.py      # 异常定义
└── tests/                  # 测试模块
    ├── __init__.py
    ├── test_document_service.py
    └── test_search_service.py
```

### 设计原则

- **高内聚低耦合**: 每个模块职责单一，接口清晰
- **分层架构**: API层 → 服务层 → 客户端层，依赖方向单一
- **配置驱动**: 所有配置通过环境变量管理
- **异常处理**: 统一的异常体系和错误处理
- **测试覆盖**: 完整的单元测试和集成测试

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目（如果需要）
git clone <repository-url>
cd zhi-manticore2

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 一键设置环境
./setup_manticore_env.sh
```

### 2. 手动设置（可选）

```bash
# 安装依赖
pip install -r requirements-manticore.txt

# 启动 Manticore Search
docker-compose up -d

# 等待服务启动
sleep 10

# 测试模块
python3 test_manticore_module.py
```

### 3. 启动 API 服务

```bash
# 开发模式（自动重载）
python3 run_manticore_api.py --reload

# 生产模式
python3 run_manticore_api.py --host 0.0.0.0 --port 8000 --workers 4
```

### 4. 访问 API

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health
- **根路径**: http://localhost:8000/

## 📚 功能特性

### 核心功能

- ✅ **文档管理**: 创建、读取、更新、删除文档
- ✅ **全文搜索**: 基于 Manticore 的高性能全文搜索
- ✅ **向量搜索**: 支持 128 维向量的 KNN 搜索
- ✅ **混合搜索**: 全文搜索 + 向量搜索的组合
- ✅ **批量操作**: 批量创建和更新文档
- ✅ **高亮摘要**: 搜索结果的高亮显示
- ✅ **健康检查**: 完整的系统健康监控
- ✅ **统计信息**: 文档和搜索统计

### 高级特性

- 🔧 **配置管理**: 基于 Pydantic 的类型安全配置
- 📝 **日志系统**: 结构化日志和彩色输出
- 🚨 **异常处理**: 统一的异常体系
- 🧪 **测试覆盖**: 完整的单元测试和集成测试
- 📊 **性能监控**: 搜索耗时和系统指标
- 🔒 **类型安全**: 全面的 Python 类型注解

## 🔧 配置说明

### 环境变量

```bash
# Manticore 连接配置
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_HTTP_PORT=9308

# 搜索配置
MANTICORE_DEFAULT_SEARCH_LIMIT=20
MANTICORE_VECTOR_DIMENSIONS=128

# 日志配置
MANTICORE_LOG_LEVEL=INFO

# API 配置
MANTICORE_API_TITLE="Manticore Search API"
```

### 配置文件

复制并编辑环境变量文件：

```bash
cp .env.example .env
# 编辑 .env 文件
```

## 📖 API 使用示例

### 创建文档

```python
import requests

# 创建文档
doc_data = {
    "title": "Python 编程指南",
    "content": "Python 是一种高级编程语言...",
    "category": "编程"
}

response = requests.post("http://localhost:8000/api/v1/documents", json=doc_data)
print(response.json())
```

### 搜索文档

```python
# 全文搜索
search_data = {
    "query": "Python 编程",
    "search_type": "fulltext",
    "limit": 10
}

response = requests.post("http://localhost:8000/api/v1/search", json=search_data)
results = response.json()

for result in results["data"]["results"]:
    print(f"标题: {result['document']['title']}")
    print(f"分数: {result['document']['score']}")
```

### 向量搜索

```python
# 向量搜索
import numpy as np

query_vector = np.random.rand(128).tolist()  # 示例向量

search_data = {
    "query": "机器学习",
    "search_type": "vector",
    "query_vector": query_vector,
    "limit": 5
}

response = requests.post("http://localhost:8000/api/v1/search", json=search_data)
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python3 -m pytest manticore_search/tests/ -v

# 运行特定测试
python3 -m pytest manticore_search/tests/test_document_service.py -v

# 运行模块测试
python3 test_manticore_module.py
```

### 测试覆盖

- 文档服务测试: CRUD 操作、批量操作、统计信息
- 搜索服务测试: 全文搜索、向量搜索、混合搜索
- 健康检查测试: 连接状态、系统指标
- 集成测试: 端到端功能测试

## 🔍 监控和调试

### 健康检查

```bash
curl http://localhost:8000/api/v1/health
```

### 日志查看

```bash
# API 服务日志
python3 run_manticore_api.py --log-level debug

# Manticore 服务日志
docker-compose logs manticore
```

### 性能监控

- 搜索响应时间统计
- 系统资源使用情况
- 数据库连接状态

## 🚀 部署

### Docker 部署

```bash
# 构建镜像
docker build -t manticore-search-api .

# 运行容器
docker run -p 8000:8000 manticore-search-api
```

### 生产环境

```bash
# 使用 Gunicorn
pip install gunicorn
gunicorn manticore_search.api.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 🤝 开发指南

### 代码规范

- 使用 Black 进行代码格式化
- 使用 isort 进行导入排序
- 使用 flake8 进行代码检查
- 遵循 PEP 8 编码规范

### 贡献流程

1. Fork 项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🙋‍♂️ 支持

如有问题或建议，请创建 Issue 或联系维护者。
