#!/usr/bin/env python3
"""
向量搜索功能测试脚本

测试 Manticore Search 模块的向量搜索功能
"""

import sys
import asyncio
import numpy as np
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from manticore_search import (
    DocumentService,
    SearchService,
    DocumentCreate,
    create_vector_search_request,
    create_hybrid_search_request,
    create_sample_embedding,
    get_settings,
    get_logger
)


def generate_test_vectors(num_vectors: int = 5, dimensions: int = 128) -> list:
    """生成测试向量数据"""
    vectors = []
    
    # 生成不同类型的向量
    for i in range(num_vectors):
        if i == 0:
            # 零向量
            vector = [0.0] * dimensions
        elif i == 1:
            # 单位向量
            vector = [1.0] + [0.0] * (dimensions - 1)
        elif i == 2:
            # 随机向量
            vector = np.random.normal(0, 1, dimensions).tolist()
        elif i == 3:
            # 正弦波向量
            vector = [np.sin(j * 0.1) for j in range(dimensions)]
        else:
            # 基于文本的确定性向量
            text = f"测试向量 {i}"
            vector = create_sample_embedding(text, dimensions)
        
        vectors.append(vector)
    
    return vectors


async def test_vector_search_functionality():
    """测试向量搜索功能"""
    logger = get_logger("vector_test")
    settings = get_settings()
    
    logger.info("🧪 开始测试向量搜索功能...")
    
    try:
        # 初始化服务
        document_service = DocumentService(settings)
        search_service = SearchService(settings)
        
        # 生成测试向量
        test_vectors = generate_test_vectors(5, settings.vector_dimensions)
        logger.info(f"✅ 生成了 {len(test_vectors)} 个测试向量")
        
        # 创建测试文档
        test_documents = []
        document_data = [
            {
                "title": "机器学习基础",
                "content": "机器学习是人工智能的一个重要分支，通过算法让计算机从数据中学习模式。",
                "category": "AI",
                "vector_idx": 0
            },
            {
                "title": "深度学习原理",
                "content": "深度学习使用神经网络来模拟人脑的学习过程，在图像识别等领域表现出色。",
                "category": "AI", 
                "vector_idx": 1
            },
            {
                "title": "自然语言处理",
                "content": "自然语言处理技术让计算机能够理解和生成人类语言，包括文本分析和语音识别。",
                "category": "NLP",
                "vector_idx": 2
            },
            {
                "title": "计算机视觉",
                "content": "计算机视觉技术使计算机能够识别和理解图像内容，广泛应用于自动驾驶等领域。",
                "category": "CV",
                "vector_idx": 3
            },
            {
                "title": "数据科学方法",
                "content": "数据科学结合统计学、机器学习和领域知识来从数据中提取有价值的洞察。",
                "category": "DataScience",
                "vector_idx": 4
            }
        ]
        
        logger.info("📝 创建测试文档...")
        for doc_data in document_data:
            doc_create = DocumentCreate(
                title=doc_data["title"],
                content=doc_data["content"],
                category=doc_data["category"],
                embedding=test_vectors[doc_data["vector_idx"]]
            )
            
            try:
                doc = document_service.create_document(doc_create)
                test_documents.append(doc)
                logger.info(f"   ✅ 创建文档: {doc.title} (ID: {doc.id})")
            except Exception as e:
                logger.error(f"   ❌ 创建文档失败: {doc_data['title']} - {e}")
        
        if not test_documents:
            logger.error("❌ 没有成功创建任何测试文档")
            return False
        
        # 测试向量搜索
        logger.info("🔍 测试向量搜索...")
        
        # 使用第一个文档的向量作为查询向量
        query_vector = test_vectors[0]
        
        # 创建向量搜索请求
        vector_request = create_vector_search_request(
            "机器学习",
            query_vector,
            limit=3
        )
        
        try:
            vector_response = search_service.search(vector_request)
            logger.info(f"✅ 向量搜索完成:")
            logger.info(f"   - 找到 {vector_response.total} 个结果")
            logger.info(f"   - 搜索耗时: {vector_response.took:.2f}ms")
            logger.info(f"   - 搜索类型: {vector_response.search_type}")
            
            for i, result in enumerate(vector_response.results, 1):
                logger.info(f"   {i}. {result.document.title}")
                logger.info(f"      分数: {result.document.score:.4f}")
                logger.info(f"      分类: {result.document.category}")
        
        except Exception as e:
            logger.error(f"❌ 向量搜索失败: {e}")
        
        # 测试混合搜索
        logger.info("🔍 测试混合搜索...")
        
        try:
            hybrid_request = create_hybrid_search_request(
                "深度学习",
                query_vector,
                vector_weight=0.7,
                limit=3
            )
            
            hybrid_response = search_service.search(hybrid_request)
            logger.info(f"✅ 混合搜索完成:")
            logger.info(f"   - 找到 {hybrid_response.total} 个结果")
            logger.info(f"   - 搜索耗时: {hybrid_response.took:.2f}ms")
            logger.info(f"   - 向量权重: 0.7")
            
            for i, result in enumerate(hybrid_response.results, 1):
                logger.info(f"   {i}. {result.document.title}")
                logger.info(f"      混合分数: {result.document.score:.4f}")
        
        except Exception as e:
            logger.error(f"❌ 混合搜索失败: {e}")
        
        # 测试向量相似度
        logger.info("📊 测试向量相似度计算...")
        
        try:
            # 计算向量之间的余弦相似度
            def cosine_similarity(v1, v2):
                dot_product = sum(a * b for a, b in zip(v1, v2))
                norm_v1 = sum(a * a for a in v1) ** 0.5
                norm_v2 = sum(a * a for a in v2) ** 0.5
                return dot_product / (norm_v1 * norm_v2) if norm_v1 * norm_v2 != 0 else 0
            
            logger.info("   向量相似度矩阵:")
            for i, vec1 in enumerate(test_vectors[:3]):
                similarities = []
                for j, vec2 in enumerate(test_vectors[:3]):
                    sim = cosine_similarity(vec1, vec2)
                    similarities.append(f"{sim:.3f}")
                logger.info(f"   向量{i}: [{', '.join(similarities)}]")
        
        except Exception as e:
            logger.error(f"❌ 相似度计算失败: {e}")
        
        # 清理测试数据
        logger.info("🧹 清理测试数据...")
        for doc in test_documents:
            try:
                document_service.delete_document(doc.id)
                logger.info(f"   ✅ 删除文档: {doc.title}")
            except Exception as e:
                logger.warning(f"   ⚠️ 删除文档失败: {doc.title} - {e}")
        
        logger.info("🎉 向量搜索功能测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 向量搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_vector_operations():
    """测试向量操作"""
    logger = get_logger("vector_ops")
    
    logger.info("🧮 测试向量操作...")
    
    try:
        # 测试向量生成
        dimensions = 128
        text_samples = [
            "机器学习算法",
            "深度神经网络", 
            "自然语言处理",
            "计算机视觉",
            "数据挖掘技术"
        ]
        
        vectors = []
        for text in text_samples:
            vector = create_sample_embedding(text, dimensions)
            vectors.append(vector)
            logger.info(f"   ✅ 生成向量: {text} -> [{vector[0]:.3f}, {vector[1]:.3f}, ...]")
        
        # 测试向量格式化
        for i, vector in enumerate(vectors[:2]):
            vector_str = '(' + ','.join(map(str, vector)) + ')'
            logger.info(f"   ✅ 格式化向量{i}: 长度={len(vector_str)} 字符")
        
        logger.info("✅ 向量操作测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 向量操作测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Manticore Search 向量搜索功能测试")
    print("=" * 60)
    
    # 测试向量操作
    ops_ok = test_vector_operations()
    if not ops_ok:
        print("❌ 向量操作测试失败，退出")
        sys.exit(1)
    
    # 测试向量搜索功能
    try:
        functionality_ok = asyncio.run(test_vector_search_functionality())
        if functionality_ok:
            print("\n🎉 所有向量搜索测试通过！")
            print("向量搜索功能已优化并准备就绪")
        else:
            print("\n❌ 向量搜索功能测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
