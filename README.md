# Manticore Search Demo

这是一个 Manticore Search 的完整演示项目，展示了以下核心功能：

- ✅ 基础 CRUD 操作（创建、读取、更新、删除）
- ✅ 全文搜索功能
- ✅ 向量搜索（KNN）功能
- ✅ 混合搜索（全文 + 向量）
- ✅ 摘要存储和检索
- ✅ 高亮显示功能

## 快速开始

### 方法一：一键运行（推荐）

```bash
# 给脚本执行权限
chmod +x run_demo.sh

# 运行演示
./run_demo.sh
```

### 方法二：手动步骤


1. **启动 Manticore Search**

```bash
docker-compose up -d
```


2. **安装 Python 依赖**

```bash
pip install -r requirements.txt
```


3. **运行快速测试**

```bash
python3 test_manticore.py
```


4. **运行完整演示**

```bash
python3 manticore_demo.py
```

## 项目结构

```
.
├── manticore_demo.py      # 主演示脚本
├── test_manticore.py      # 快速测试脚本
├── docker-compose.yml     # Docker 配置
├── manticore.conf         # Manticore 配置文件
├── requirements.txt       # Python 依赖
├── run_demo.sh           # 一键运行脚本
└── README.md             # 说明文档
```

## 演示内容

### 1. 表结构创建

创建包含以下字段的知识库表：

- `id`: 文档ID
- `title`: 标题（全文索引 + 存储）
- `content`: 内容（全文索引 + 存储）
- `category`: 分类（全文索引 + 存储）
- `embedding`: 128维向量（KNN搜索）
- `created_at`: 创建时间

### 2. 数据插入

插入5条示例文档，涵盖：

- Manticore Search 介绍
- 向量搜索原理
- Python 客户端使用
- 实时索引特性
- 分布式搜索

### 3. 搜索演示

#### 全文搜索

```sql
SELECT id, title, content, WEIGHT() as relevance_score
FROM knowledge_base 
WHERE MATCH('搜索引擎')
ORDER BY relevance_score DESC
```

#### 向量搜索

```sql
SELECT id, title, content,
       DOT(embedding, [0.1, 0.2, ...]) as similarity_score
FROM knowledge_base
ORDER BY similarity_score DESC
```

#### 混合搜索

```sql
SELECT id, title, content,
       WEIGHT() as text_score,
       DOT(embedding, [0.1, 0.2, ...]) as vector_score,
       (WEIGHT() * 0.7 + DOT(embedding, [0.1, 0.2, ...]) * 0.3) as combined_score
FROM knowledge_base
WHERE MATCH('搜索技术')
ORDER BY combined_score DESC
```

### 4. CRUD 操作演示

- **CREATE**: 插入新文档
- **READ**: 查询文档
- **UPDATE**: 更新文档标题
- **DELETE**: 删除文档

### 5. 摘要和高亮

使用 `HIGHLIGHT()` 函数生成带高亮的文本摘要：

```sql
SELECT HIGHLIGHT({limit=100, before_match='<b>', after_match='</b>'}, content, '搜索引擎') as snippet
FROM knowledge_base
WHERE MATCH('搜索引擎')
```

## 技术特点

### 向量搜索配置

- **算法**: HNSW (Hierarchical Navigable Small World)
- **维度**: 128维
- **相似度**: 余弦相似度 (cosine)
- **存储引擎**: columnar（列式存储）

### 连接方式

- **协议**: MySQL 协议（端口 9306）
- **客户端**: PyMySQL
- **API**: 也支持 HTTP JSON API（端口 9308）

## 故障排除

### 常见问题


1. **连接失败**

```bash
# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs manticore
```


2. **端口冲突**

```bash
# 检查端口占用
netstat -tulpn | grep :9306

# 修改 docker-compose.yml 中的端口映射
```


3. **权限问题**

```bash
# 给脚本执行权限
chmod +x run_demo.sh
```

### 手动连接测试

```bash
# 使用 MySQL 客户端连接
mysql -h127.0.0.1 -P9306

# 或使用 Docker 内的客户端
docker exec -it manticore-demo mysql
```

## 清理环境

```bash
# 停止并删除容器
docker-compose down

# 删除数据卷（可选）
docker-compose down -v
```

## 扩展功能

这个演示可以扩展为：

- 集成真实的文本向量化模型（如 sentence-transformers）
- 添加更多搜索算法（模糊搜索、自动完成）
- 实现分布式部署
- 添加 Web 界面
- 集成到实际应用中

## 参考资料

- [Manticore Search 官方文档](https://manual.manticoresearch.com/)
- [Manticore Search GitHub](https://github.com/manticoresoftware/manticoresearch)
- [Docker Hub - Manticore Search](https://hub.docker.com/r/manticoresearch/manticore)


