#!/usr/bin/env python3
"""
FastAPI 模块测试脚本
"""

import subprocess
import sys
import time
import requests
import json

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "manticore_api/requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def start_api_server():
    """启动 API 服务器"""
    print("🚀 启动 API 服务器...")
    try:
        # 启动服务器（后台运行）
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "manticore_api.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ])
        
        # 等待服务器启动
        time.sleep(5)
        
        # 检查服务器是否启动
        try:
            response = requests.get("http://localhost:8000/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ API 服务器启动成功")
                return process
            else:
                print(f"❌ API 服务器启动失败，状态码: {response.status_code}")
                process.terminate()
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ API 服务器连接失败: {e}")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ 启动 API 服务器失败: {e}")
        return None

def test_api_endpoints():
    """测试 API 端点"""
    print("🧪 测试 API 端点...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 健康检查接口正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 测试文档创建
    try:
        doc_data = {
            "title": "测试文档",
            "content": "这是一个测试文档的内容",
            "category": "测试"
        }
        response = requests.post(f"{base_url}/documents", json=doc_data)
        if response.status_code == 200:
            print("✅ 文档创建接口正常")
            doc_id = response.json().get("data", {}).get("id")
            print(f"   创建的文档ID: {doc_id}")
        else:
            print(f"❌ 文档创建失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 文档创建异常: {e}")
    
    # 测试搜索
    try:
        search_data = {
            "query": "测试",
            "limit": 10
        }
        response = requests.post(f"{base_url}/search", json=search_data)
        if response.status_code == 200:
            print("✅ 搜索接口正常")
            results = response.json()
            print(f"   搜索结果数量: {results.get('total', 0)}")
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 搜索异常: {e}")

def main():
    """主函数"""
    print("🚀 开始 FastAPI 模块测试")
    print("="*50)
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 启动服务器
    server_process = start_api_server()
    if not server_process:
        sys.exit(1)
    
    try:
        # 测试 API
        test_api_endpoints()
        
        print("\n🎉 FastAPI 模块测试完成！")
        print("\n📋 下一步操作:")
        print("1. 访问 http://localhost:8000/docs 查看 API 文档")
        print("2. 使用 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        print("\n⏳ 服务器正在运行，按 Ctrl+C 停止...")
        server_process.wait()
        
    except KeyboardInterrupt:
        print("\n⏹️  停止服务器...")
        server_process.terminate()
        server_process.wait()
        print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
