#!/bin/bash

# Manticore Search 模块环境设置脚本

set -e

echo "🚀 设置 Manticore Search 模块环境..."

# 检查 Python 版本
echo "🐍 检查 Python 版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python 3 未安装或不可用"
    exit 1
fi

# 检查虚拟环境
if [ -n "$VIRTUAL_ENV" ]; then
    echo "✅ 检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "⚠️  建议在虚拟环境中运行"
    echo "创建虚拟环境: python3 -m venv venv"
    echo "激活虚拟环境: source venv/bin/activate"
fi

# 安装依赖
echo "📦 安装 Python 依赖..."
pip install -r requirements-manticore.txt

# 检查 Docker
echo "🐳 检查 Docker..."
if command -v docker &> /dev/null; then
    echo "✅ Docker 已安装"
    
    # 检查 Docker Compose
    if command -v docker-compose &> /dev/null; then
        echo "✅ Docker Compose 已安装"
    else
        echo "❌ Docker Compose 未安装"
        exit 1
    fi
else
    echo "❌ Docker 未安装"
    exit 1
fi

# 启动 Manticore Search
echo "🔍 启动 Manticore Search..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待 Manticore Search 启动..."
sleep 10

# 检查服务状态
if docker-compose ps | grep -q "Up"; then
    echo "✅ Manticore Search 启动成功"
else
    echo "❌ Manticore Search 启动失败"
    docker-compose logs
    exit 1
fi

# 设置权限
echo "🔧 设置脚本权限..."
chmod +x run_manticore_api.py
chmod +x test_manticore_module.py

# 创建环境变量文件模板
echo "📝 创建环境变量文件模板..."
cat > .env.example << EOF
# Manticore Search 配置
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_USER=
MANTICORE_PASSWORD=
MANTICORE_CHARSET=utf8mb4

# 连接池配置
MANTICORE_CONNECTION_POOL_SIZE=10
MANTICORE_CONNECTION_TIMEOUT=30
MANTICORE_READ_TIMEOUT=30

# 搜索配置
MANTICORE_DEFAULT_SEARCH_LIMIT=20
MANTICORE_MAX_SEARCH_LIMIT=100
MANTICORE_SNIPPET_LENGTH=200
MANTICORE_SNIPPET_AROUND=5

# 向量搜索配置
MANTICORE_VECTOR_DIMENSIONS=128
MANTICORE_KNN_SEARCH_LIMIT=10

# 日志配置
MANTICORE_LOG_LEVEL=INFO

# API 配置
MANTICORE_API_TITLE="Manticore Search API"
MANTICORE_API_DESCRIPTION="基于 Manticore Search 的高内聚搜索引擎接口"
MANTICORE_API_VERSION="1.0.0"
MANTICORE_API_PREFIX="/api/v1"

# 健康检查配置
MANTICORE_HEALTH_CHECK_TIMEOUT=5

# 表配置
MANTICORE_DEFAULT_TABLE_NAME=knowledge_base
EOF

echo "✅ 环境变量模板已创建: .env.example"

# 运行测试
echo "🧪 运行模块测试..."
python3 test_manticore_module.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 环境设置完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 复制并编辑环境变量文件: cp .env.example .env"
    echo "2. 启动 API 服务: python3 run_manticore_api.py"
    echo "3. 访问 API 文档: http://localhost:8000/docs"
    echo "4. 运行完整测试: python3 -m pytest manticore_search/tests/ -v"
    echo ""
    echo "🔧 可用命令:"
    echo "- 启动 API: python3 run_manticore_api.py --reload"
    echo "- 运行测试: python3 test_manticore_module.py"
    echo "- 停止服务: docker-compose down"
else
    echo "❌ 模块测试失败，请检查配置"
    exit 1
fi
