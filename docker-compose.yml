services:
  manticore:
    image: manticoresearch/manticore:latest
    container_name: manticore-demo
    ports:
      - "9306:9306"  # MySQL protocol
      - "9308:9308"  # HTTP JSON API
      - "9312:9312"  # SphinxAPI
    volumes:
      - manticore_data:/var/lib/manticore
      - ./manticore.conf:/etc/manticoresearch/manticore.conf
    environment:
      - EXTRA=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "mysql -h127.0.0.1 -P9306 -e 'SHOW TABLES' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  manticore_data:
    driver: local
