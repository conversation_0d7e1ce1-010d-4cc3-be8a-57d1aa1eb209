#!/usr/bin/env python3
"""
技术总结验证脚本
验证 TECHNICAL_SUMMARY.md 中提到的所有技术点和最佳实践
"""

import pymysql
import time
import sys
import json
from typing import Dict, List, Any, Optional

class TechnicalValidator:
    def __init__(self):
        self.connection = None
        self.test_results = {}
        
    def connect(self) -> bool:
        """验证连接管理最佳实践"""
        print("🔌 验证连接管理...")
        try:
            # 按照技术总结中的最佳实践连接
            self.connection = pymysql.connect(
                host='localhost',
                port=9306,
                user='',
                password='',
                charset='utf8mb4',
                autocommit=True  # 技术总结推荐的配置
            )
            self.test_results['connection'] = True
            print("✅ 连接管理验证通过")
            return True
        except Exception as e:
            self.test_results['connection'] = False
            print(f"❌ 连接失败: {e}")
            return False
    
    def validate_table_creation(self) -> bool:
        """验证表创建和中文分词配置"""
        print("\n🏗️  验证表创建和中文分词配置...")
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 删除测试表
            cursor.execute("DROP TABLE IF EXISTS tech_validation")
            
            # 按照技术总结中的推荐结构创建表
            create_sql = """
            CREATE TABLE tech_validation (
                id BIGINT,
                title TEXT INDEXED STORED,
                content TEXT INDEXED STORED,
                category STRING INDEXED STORED,
                created_at TIMESTAMP
            ) morphology='icu_chinese'
            """
            
            cursor.execute(create_sql)
            
            # 验证表结构
            cursor.execute("DESCRIBE tech_validation")
            fields = cursor.fetchall()
            
            expected_fields = ['id', 'title', 'content', 'category', 'created_at']
            actual_fields = [field['Field'] for field in fields]
            
            if all(field in actual_fields for field in expected_fields):
                self.test_results['table_creation'] = True
                print("✅ 表创建和中文分词配置验证通过")
                print(f"   表结构: {actual_fields}")
                return True
            else:
                self.test_results['table_creation'] = False
                print(f"❌ 表结构不匹配，期望: {expected_fields}, 实际: {actual_fields}")
                return False
                
        except Exception as e:
            self.test_results['table_creation'] = False
            print(f"❌ 表创建验证失败: {e}")
            return False
    
    def validate_crud_operations(self) -> bool:
        """验证 CRUD 操作最佳实践"""
        print("\n⚙️  验证 CRUD 操作...")
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # CREATE - 使用参数化查询（技术总结推荐）
            print("📝 验证 CREATE 操作...")
            current_time = int(time.time())
            insert_sql = """
            INSERT INTO tech_validation (id, title, content, category, created_at)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(insert_sql, (1, "测试标题", "测试内容", "测试分类", current_time))
            
            # READ - 验证数据插入
            print("📖 验证 READ 操作...")
            cursor.execute("SELECT * FROM tech_validation WHERE id = 1")
            result = cursor.fetchone()
            if not result:
                raise Exception("数据插入失败")
            
            # UPDATE - 使用 REPLACE（技术总结中的限制解决方案）
            print("✏️  验证 UPDATE 操作（使用 REPLACE）...")
            replace_sql = """
            REPLACE INTO tech_validation (id, title, content, category, created_at)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(replace_sql, (1, "更新的标题", "更新的内容", "更新的分类", current_time))
            
            # 验证更新
            cursor.execute("SELECT title FROM tech_validation WHERE id = 1")
            result = cursor.fetchone()
            if result['title'] != "更新的标题":
                raise Exception("REPLACE 更新失败")
            
            # DELETE
            print("🗑️  验证 DELETE 操作...")
            cursor.execute("DELETE FROM tech_validation WHERE id = 1")
            cursor.execute("SELECT * FROM tech_validation WHERE id = 1")
            result = cursor.fetchone()
            if result:
                raise Exception("删除操作失败")
            
            self.test_results['crud_operations'] = True
            print("✅ CRUD 操作验证通过")
            return True
            
        except Exception as e:
            self.test_results['crud_operations'] = False
            print(f"❌ CRUD 操作验证失败: {e}")
            return False
    
    def validate_fulltext_search(self) -> bool:
        """验证全文搜索功能"""
        print("\n🔍 验证全文搜索功能...")
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 插入测试数据
            test_data = [
                (1, "Python 开发指南", "Python 是一种强大的编程语言", "技术", int(time.time())),
                (2, "Java 基础教程", "Java 是面向对象的编程语言", "技术", int(time.time())),
                (3, "数据库设计", "关系型数据库的设计原则", "数据库", int(time.time()))
            ]
            
            for data in test_data:
                cursor.execute("""
                    INSERT INTO tech_validation (id, title, content, category, created_at)
                    VALUES (%s, %s, %s, %s, %s)
                """, data)
            
            # 测试全文搜索（按照技术总结中的语法）
            search_sql = """
            SELECT id, title, content, WEIGHT() as relevance_score
            FROM tech_validation 
            WHERE MATCH(%s)
            ORDER BY relevance_score DESC
            LIMIT 10
            """
            
            # 测试英文搜索
            cursor.execute(search_sql, ("Python",))
            results = cursor.fetchall()
            
            if results and len(results) > 0:
                print(f"✅ 英文搜索验证通过，找到 {len(results)} 条结果")
                for result in results:
                    print(f"   - {result['title']} (相关度: {result['relevance_score']})")
            else:
                print("⚠️  英文搜索未找到结果")
            
            # 测试中文搜索
            cursor.execute(search_sql, ("编程",))
            results = cursor.fetchall()
            
            if results and len(results) > 0:
                print(f"✅ 中文搜索验证通过，找到 {len(results)} 条结果")
                self.test_results['chinese_search'] = True
            else:
                print("⚠️  中文搜索效果需要优化（符合技术总结中的发现）")
                self.test_results['chinese_search'] = False
            
            self.test_results['fulltext_search'] = True
            print("✅ 全文搜索功能验证完成")
            return True
            
        except Exception as e:
            self.test_results['fulltext_search'] = False
            print(f"❌ 全文搜索验证失败: {e}")
            return False
    
    def validate_highlight_feature(self) -> bool:
        """验证高亮摘要功能"""
        print("\n📄 验证高亮摘要功能...")
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 按照技术总结中的语法测试高亮功能
            highlight_sql = """
            SELECT id, title,
                   HIGHLIGHT({limit=100, before_match='<b>', after_match='</b>'}, content, %s) as snippet
            FROM tech_validation
            WHERE MATCH(%s)
            LIMIT 2
            """
            
            cursor.execute(highlight_sql, ("Python", "Python"))
            results = cursor.fetchall()
            
            if results:
                print("✅ 高亮摘要功能验证通过")
                for result in results:
                    print(f"   - {result['title']}")
                    print(f"     摘要: {result['snippet']}")
                self.test_results['highlight'] = True
                return True
            else:
                print("⚠️  高亮摘要未找到匹配结果")
                self.test_results['highlight'] = False
                return False
                
        except Exception as e:
            self.test_results['highlight'] = False
            print(f"❌ 高亮摘要验证失败: {e}")
            return False
    
    def validate_error_handling(self) -> bool:
        """验证错误处理机制"""
        print("\n🚨 验证错误处理机制...")
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 测试语法错误处理
            try:
                cursor.execute("INVALID SQL SYNTAX")
                print("❌ 错误处理验证失败：应该抛出异常")
                self.test_results['error_handling'] = False
                return False
            except Exception as e:
                print(f"✅ 语法错误正确捕获: {type(e).__name__}")
            
            # 测试不存在的表
            try:
                cursor.execute("SELECT * FROM non_existent_table")
                print("❌ 错误处理验证失败：应该抛出异常")
                self.test_results['error_handling'] = False
                return False
            except Exception as e:
                print(f"✅ 表不存在错误正确捕获: {type(e).__name__}")
            
            self.test_results['error_handling'] = True
            print("✅ 错误处理机制验证通过")
            return True
            
        except Exception as e:
            self.test_results['error_handling'] = False
            print(f"❌ 错误处理验证失败: {e}")
            return False
    
    def validate_performance_basics(self) -> bool:
        """验证基础性能特性"""
        print("\n⚡ 验证基础性能特性...")
        try:
            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            
            # 测试查询性能
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) as total FROM tech_validation")
            result = cursor.fetchone()
            end_time = time.time()
            
            query_time = (end_time - start_time) * 1000  # 转换为毫秒
            print(f"✅ 查询性能: {query_time:.2f}ms (总记录数: {result['total']})")
            
            # 测试分页查询
            cursor.execute("SELECT * FROM tech_validation LIMIT 2 OFFSET 0")
            results = cursor.fetchall()
            print(f"✅ 分页查询: 返回 {len(results)} 条记录")
            
            self.test_results['performance'] = True
            print("✅ 基础性能特性验证通过")
            return True
            
        except Exception as e:
            self.test_results['performance'] = False
            print(f"❌ 性能验证失败: {e}")
            return False
    
    def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        try:
            if self.connection:
                cursor = self.connection.cursor()
                cursor.execute("DROP TABLE IF EXISTS tech_validation")
                print("✅ 测试数据清理完成")
        except Exception as e:
            print(f"⚠️  清理过程中出现警告: {e}")
    
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("📊 技术总结验证报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        print(f"总测试项: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print("\n🎯 验证结论:")
        if passed_tests == total_tests:
            print("✅ 技术总结中的所有关键点都得到验证，可以开始模块化开发")
        else:
            print("⚠️  部分功能需要进一步优化，但核心功能已验证")
        
        print("\n📋 下一步建议:")
        print("1. 基于验证结果开始 FastAPI 模块化开发")
        print("2. 重点关注已验证的功能进行 API 封装")
        print("3. 对未完全验证的功能制定优化计划")
        
        return passed_tests == total_tests

def main():
    """主函数"""
    print("🚀 开始验证技术总结文档")
    print("="*60)
    
    validator = TechnicalValidator()
    
    try:
        # 执行所有验证步骤
        if not validator.connect():
            sys.exit(1)
        
        validator.validate_table_creation()
        validator.validate_crud_operations()
        validator.validate_fulltext_search()
        validator.validate_highlight_feature()
        validator.validate_error_handling()
        validator.validate_performance_basics()
        
        # 生成报告
        success = validator.generate_report()
        
        if success:
            print("\n🎉 技术总结验证完成，可以开始模块化开发！")
        else:
            print("\n⚠️  技术总结验证完成，建议优化后再开始模块化开发")
        
    except KeyboardInterrupt:
        print("\n⏹️  验证被用户中断")
    except Exception as e:
        print(f"\n❌ 验证过程出现异常: {e}")
    finally:
        validator.cleanup()
        if validator.connection:
            validator.connection.close()

if __name__ == "__main__":
    main()
