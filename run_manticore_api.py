#!/usr/bin/env python3
"""
Manticore Search API 启动脚本

启动 FastAPI 服务器
"""

import os
import sys
import uvicorn
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from manticore_search.utils import get_settings, get_logger


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动 Manticore Search API 服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--log-level", default="info", help="日志级别")
    
    args = parser.parse_args()
    
    # 获取配置
    settings = get_settings()
    logger = get_logger("startup")
    
    logger.info("🚀 启动 Manticore Search API 服务...")
    logger.info(f"配置信息:")
    logger.info(f"  - Manticore 主机: {settings.manticore_host}:{settings.manticore_port}")
    logger.info(f"  - API 服务: {args.host}:{args.port}")
    logger.info(f"  - 日志级别: {args.log_level}")
    logger.info(f"  - 自动重载: {args.reload}")
    
    # 启动服务器
    uvicorn.run(
        "manticore_search.api.main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers if not args.reload else 1,
        log_level=args.log_level,
        access_log=True
    )


if __name__ == "__main__":
    main()
