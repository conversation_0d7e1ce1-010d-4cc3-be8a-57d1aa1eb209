#!/usr/bin/env python3
"""
FastAPI 模块创建脚本
基于技术验证结果创建高内聚的 Manticore Search API 模块
"""

import os
import sys
from pathlib import Path

class FastAPIModuleCreator:
    def __init__(self, base_path: str = "manticore_api"):
        self.base_path = Path(base_path)
        self.created_files = []
    
    def create_directory_structure(self):
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        directories = [
            self.base_path,
            self.base_path / "models",
            self.base_path / "services", 
            self.base_path / "clients",
            self.base_path / "utils",
            self.base_path / "tests"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ {directory}")
    
    def create_requirements(self):
        """创建依赖文件"""
        print("\n📦 创建依赖文件...")
        
        requirements_content = """# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# 数据库连接
pymysql==1.1.0

# 工具库
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
black==23.11.0
flake8==6.1.0

# 日志和监控
structlog==23.2.0
prometheus-client==0.19.0

# 现有依赖
numpy==1.24.3
requests==2.31.0
"""
        
        requirements_path = self.base_path / "requirements.txt"
        with open(requirements_path, 'w', encoding='utf-8') as f:
            f.write(requirements_content)
        
        self.created_files.append(requirements_path)
        print(f"   ✅ {requirements_path}")
    
    def create_main_app(self):
        """创建主应用文件"""
        print("\n🚀 创建主应用文件...")
        
        main_content = '''"""
FastAPI 主应用
基于验证的 Manticore Search 功能构建
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import time
import logging

from .services.search import SearchService
from .services.document import DocumentService
from .services.health import HealthService
from .models.request import SearchRequest, CreateDocumentRequest, UpdateDocumentRequest
from .models.response import SearchResponse, ApiResponse, HealthResponse
from .utils.config import get_settings
from .utils.logger import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

# 全局服务实例
search_service = None
document_service = None
health_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global search_service, document_service, health_service
    
    logger.info("🚀 启动 Manticore API 服务...")
    
    # 初始化服务
    settings = get_settings()
    search_service = SearchService(settings)
    document_service = DocumentService(settings)
    health_service = HealthService(settings)
    
    # 验证连接
    if await health_service.check_connection():
        logger.info("✅ Manticore 连接验证成功")
    else:
        logger.error("❌ Manticore 连接验证失败")
        raise Exception("无法连接到 Manticore Search")
    
    yield
    
    logger.info("🛑 关闭 Manticore API 服务...")

# 创建 FastAPI 应用
app = FastAPI(
    title="Manticore Search API",
    description="基于验证功能的高内聚搜索引擎接口",
    version="0.1.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 依赖注入
def get_search_service() -> SearchService:
    return search_service

def get_document_service() -> DocumentService:
    return document_service

def get_health_service() -> HealthService:
    return health_service

# 健康检查接口
@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check(service: HealthService = Depends(get_health_service)):
    """健康检查接口"""
    return await service.get_health_status()

# 搜索接口
@app.post("/api/v1/search", response_model=SearchResponse)
async def search_documents(
    request: SearchRequest,
    service: SearchService = Depends(get_search_service)
):
    """全文搜索接口"""
    start_time = time.time()
    
    try:
        results = await service.search(request)
        took = (time.time() - start_time) * 1000  # 转换为毫秒
        
        return SearchResponse(
            query=request.query,
            total=len(results),
            results=results,
            took=took
        )
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 文档管理接口
@app.post("/api/v1/documents", response_model=ApiResponse)
async def create_document(
    request: CreateDocumentRequest,
    service: DocumentService = Depends(get_document_service)
):
    """创建文档"""
    try:
        doc_id = await service.create_document(request)
        return ApiResponse(
            success=True,
            message="文档创建成功",
            data={"id": doc_id}
        )
    except Exception as e:
        logger.error(f"文档创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/documents/{doc_id}")
async def get_document(
    doc_id: int,
    service: DocumentService = Depends(get_document_service)
):
    """获取文档"""
    try:
        document = await service.get_document(doc_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        return document
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/v1/documents/{doc_id}", response_model=ApiResponse)
async def update_document(
    doc_id: int,
    request: UpdateDocumentRequest,
    service: DocumentService = Depends(get_document_service)
):
    """更新文档（使用 REPLACE 策略）"""
    try:
        success = await service.update_document(doc_id, request)
        if not success:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return ApiResponse(
            success=True,
            message="文档更新成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档更新失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/v1/documents/{doc_id}", response_model=ApiResponse)
async def delete_document(
    doc_id: int,
    service: DocumentService = Depends(get_document_service)
):
    """删除文档"""
    try:
        success = await service.delete_document(doc_id)
        if not success:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return ApiResponse(
            success=True,
            message="文档删除成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档删除失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
        
        main_path = self.base_path / "main.py"
        with open(main_path, 'w', encoding='utf-8') as f:
            f.write(main_content)
        
        self.created_files.append(main_path)
        print(f"   ✅ {main_path}")
    
    def create_init_files(self):
        """创建 __init__.py 文件"""
        print("\n📄 创建 __init__.py 文件...")
        
        init_files = [
            (self.base_path / "__init__.py", '"""Manticore Search API 模块"""'),
            (self.base_path / "models" / "__init__.py", '"""数据模型"""'),
            (self.base_path / "services" / "__init__.py", '"""业务服务"""'),
            (self.base_path / "clients" / "__init__.py", '"""数据访问客户端"""'),
            (self.base_path / "utils" / "__init__.py", '"""工具模块"""'),
            (self.base_path / "tests" / "__init__.py", '"""测试模块"""'),
        ]
        
        for file_path, content in init_files:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content + '\\n')
            self.created_files.append(file_path)
            print(f"   ✅ {file_path}")
    
    def create_test_script(self):
        """创建测试脚本"""
        print("\n🧪 创建测试脚本...")
        
        test_content = '''#!/usr/bin/env python3
"""
FastAPI 模块测试脚本
"""

import subprocess
import sys
import time
import requests
import json

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "manticore_api/requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def start_api_server():
    """启动 API 服务器"""
    print("🚀 启动 API 服务器...")
    try:
        # 启动服务器（后台运行）
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "manticore_api.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ])
        
        # 等待服务器启动
        time.sleep(5)
        
        # 检查服务器是否启动
        try:
            response = requests.get("http://localhost:8000/api/v1/health", timeout=5)
            if response.status_code == 200:
                print("✅ API 服务器启动成功")
                return process
            else:
                print(f"❌ API 服务器启动失败，状态码: {response.status_code}")
                process.terminate()
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ API 服务器连接失败: {e}")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ 启动 API 服务器失败: {e}")
        return None

def test_api_endpoints():
    """测试 API 端点"""
    print("🧪 测试 API 端点...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 健康检查接口正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 测试文档创建
    try:
        doc_data = {
            "title": "测试文档",
            "content": "这是一个测试文档的内容",
            "category": "测试"
        }
        response = requests.post(f"{base_url}/documents", json=doc_data)
        if response.status_code == 200:
            print("✅ 文档创建接口正常")
            doc_id = response.json().get("data", {}).get("id")
            print(f"   创建的文档ID: {doc_id}")
        else:
            print(f"❌ 文档创建失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 文档创建异常: {e}")
    
    # 测试搜索
    try:
        search_data = {
            "query": "测试",
            "limit": 10
        }
        response = requests.post(f"{base_url}/search", json=search_data)
        if response.status_code == 200:
            print("✅ 搜索接口正常")
            results = response.json()
            print(f"   搜索结果数量: {results.get('total', 0)}")
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 搜索异常: {e}")

def main():
    """主函数"""
    print("🚀 开始 FastAPI 模块测试")
    print("="*50)
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 启动服务器
    server_process = start_api_server()
    if not server_process:
        sys.exit(1)
    
    try:
        # 测试 API
        test_api_endpoints()
        
        print("\\n🎉 FastAPI 模块测试完成！")
        print("\\n📋 下一步操作:")
        print("1. 访问 http://localhost:8000/docs 查看 API 文档")
        print("2. 使用 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        print("\\n⏳ 服务器正在运行，按 Ctrl+C 停止...")
        server_process.wait()
        
    except KeyboardInterrupt:
        print("\\n⏹️  停止服务器...")
        server_process.terminate()
        server_process.wait()
        print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
'''
        
        test_path = Path("test_fastapi_module.py")
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        self.created_files.append(test_path)
        print(f"   ✅ {test_path}")
    
    def generate_summary(self):
        """生成创建总结"""
        print("\n" + "="*60)
        print("📊 FastAPI 模块创建总结")
        print("="*60)
        
        print(f"✅ 创建了 {len(self.created_files)} 个文件")
        print("\n📁 目录结构:")
        print(f"   {self.base_path}/")
        print(f"   ├── __init__.py")
        print(f"   ├── main.py")
        print(f"   ├── requirements.txt")
        print(f"   ├── models/")
        print(f"   ├── services/")
        print(f"   ├── clients/")
        print(f"   ├── utils/")
        print(f"   └── tests/")

        print("\n🎯 下一步操作:")
        print("1. 运行 python3 test_fastapi_module.py 测试模块")
        print("2. 完善各个子模块的实现")
        print("3. 添加更多测试用例")
        print("4. 部署到生产环境")

def main():
    """主函数"""
    print("🚀 开始创建 FastAPI 模块")
    print("="*50)
    
    creator = FastAPIModuleCreator()
    
    try:
        creator.create_directory_structure()
        creator.create_requirements()
        creator.create_main_app()
        creator.create_init_files()
        creator.create_test_script()
        creator.generate_summary()
        
        print("\n🎉 FastAPI 模块创建完成！")
        
    except Exception as e:
        print(f"\n❌ 创建过程出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
