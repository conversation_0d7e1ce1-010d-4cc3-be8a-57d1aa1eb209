#!/usr/bin/env python3
"""
Manticore Search 简化演示脚本
专注于基础功能：CRUD、全文搜索、摘要
"""

import pymysql
import time
import sys

class SimpleManticoreDemo:
    def __init__(self, host='localhost', port=9306, user='', password=''):
        """初始化 Manticore Search 连接"""
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.connection = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                charset='utf8mb4',
                autocommit=True
            )
            print(f"✅ 成功连接到 Manticore Search ({self.host}:{self.port})")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def execute_sql(self, sql: str, params=None):
        """执行SQL语句并返回结果"""
        if not self.connection:
            print("❌ 数据库未连接")
            return None
            
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                print(f"🔍 执行SQL: {sql}")
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                # 获取结果
                if sql.strip().upper().startswith('SELECT') or sql.strip().upper().startswith('SHOW'):
                    results = cursor.fetchall()
                    print(f"📊 返回 {len(results)} 条记录")
                    return results
                else:
                    print("✅ SQL执行成功")
                    return []
        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return None
    
    def create_demo_table(self):
        """创建演示用的表（不包含向量字段）"""
        print("\n🏗️  创建演示表...")
        
        # 删除已存在的表
        drop_sql = "DROP TABLE IF EXISTS knowledge_base"
        self.execute_sql(drop_sql)
        
        # 创建包含文本字段的表
        create_sql = """
        CREATE TABLE knowledge_base (
            id BIGINT,
            title TEXT INDEXED STORED,
            content TEXT INDEXED STORED,
            category TEXT INDEXED STORED,
            created_at TIMESTAMP
        )
        """
        
        result = self.execute_sql(create_sql)
        if result is not None:
            print("✅ 表 'knowledge_base' 创建成功")
            return True
        return False
    
    def insert_sample_data(self):
        """插入示例数据"""
        print("\n📝 插入示例数据...")
        
        sample_data = [
            {
                'id': 1,
                'title': 'Manticore Search 介绍',
                'content': 'Manticore Search 是一个强大的全文搜索引擎，支持SQL查询、全文搜索和向量搜索功能。它可以处理大量文档并提供快速的搜索响应。',
                'category': '技术文档'
            },
            {
                'id': 2,
                'title': '向量搜索原理',
                'content': 'KNN（K-最近邻）搜索是一种基于向量相似度的搜索方法。通过计算查询向量与数据库中向量的距离，找到最相似的K个结果。',
                'category': '算法原理'
            },
            {
                'id': 3,
                'title': 'Python 客户端使用',
                'content': 'Manticore Search 提供了多种客户端接口，包括MySQL协议、HTTP JSON API等。Python可以通过pymysql等库连接使用。',
                'category': '开发指南'
            },
            {
                'id': 4,
                'title': '实时索引特性',
                'content': 'Manticore Search 支持实时索引，可以即时插入、更新和删除文档，无需重建整个索引。这使得它非常适合动态内容管理。',
                'category': '技术特性'
            },
            {
                'id': 5,
                'title': '分布式搜索',
                'content': 'Manticore Search 支持分布式部署，可以将数据分片存储在多个节点上，提供高可用性和水平扩展能力。',
                'category': '架构设计'
            }
        ]
        
        for data in sample_data:
            insert_sql = """
            INSERT INTO knowledge_base (id, title, content, category, created_at)
            VALUES (%s, %s, %s, %s, %s)
            """

            current_time = int(time.time())
            result = self.execute_sql(insert_sql, (data['id'], data['title'], data['content'], data['category'], current_time))
            if result is not None:
                print(f"✅ 插入记录: {data['title']}")
            else:
                print(f"❌ 插入失败: {data['title']}")
                return False
        
        print("✅ 所有示例数据插入完成")
        return True
    
    def demo_fulltext_search(self):
        """演示全文搜索功能"""
        print("\n🔍 演示全文搜索功能...")
        
        search_queries = [
            "搜索引擎",
            "Python 客户端",
            "向量搜索",
            "实时索引"
        ]
        
        for query in search_queries:
            print(f"\n🔎 搜索关键词: '{query}'")
            
            search_sql = """
            SELECT id, title, content, category, 
                   WEIGHT() as relevance_score
            FROM knowledge_base 
            WHERE MATCH(%s)
            ORDER BY relevance_score DESC
            LIMIT 3
            """
            
            results = self.execute_sql(search_sql, (query,))
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. [{result['category']}] {result['title']}")
                    print(f"     相关度: {result['relevance_score']}")
                    print(f"     内容: {result['content'][:100]}...")
            else:
                print("  未找到相关结果")
    
    def demo_crud_operations(self):
        """演示CRUD操作"""
        print("\n⚙️  演示CRUD操作...")
        
        # CREATE - 插入新记录
        print("📝 CREATE - 插入新记录")
        insert_sql = """
        INSERT INTO knowledge_base (id, title, content, category, created_at)
        VALUES (%s, %s, %s, %s, %s)
        """

        current_time = int(time.time())
        self.execute_sql(insert_sql, (999, "测试文档", "这是一个用于测试CRUD操作的文档", "测试", current_time))
        
        # READ - 查询记录
        print("\n📖 READ - 查询记录")
        select_sql = "SELECT id, title, category FROM knowledge_base WHERE id = 999"
        results = self.execute_sql(select_sql)
        if results:
            print(f"  找到记录: {results[0]}")
        
        # UPDATE - 更新记录
        print("\n✏️  UPDATE - 更新记录")
        update_sql = "UPDATE knowledge_base SET title = %s WHERE id = 999"
        self.execute_sql(update_sql, ("更新后的测试文档",))
        
        # 验证更新
        results = self.execute_sql(select_sql)
        if results:
            print(f"  更新后记录: {results[0]}")
        
        # DELETE - 删除记录
        print("\n🗑️  DELETE - 删除记录")
        delete_sql = "DELETE FROM knowledge_base WHERE id = 999"
        self.execute_sql(delete_sql)
        
        # 验证删除
        results = self.execute_sql(select_sql)
        if not results:
            print("  ✅ 记录已成功删除")
    
    def demo_snippets(self):
        """演示摘要生成功能"""
        print("\n📄 演示摘要生成功能...")
        
        # 使用HIGHLIGHT函数生成高亮摘要
        query = "搜索引擎"
        highlight_sql = """
        SELECT id, title, 
               HIGHLIGHT({limit=100, before_match='<b>', after_match='</b>'}, content, %s) as snippet
        FROM knowledge_base
        WHERE MATCH(%s)
        LIMIT 2
        """
        
        results = self.execute_sql(highlight_sql, (query, query))
        if results:
            print(f"🔎 关键词 '{query}' 的高亮摘要:")
            for result in results:
                print(f"  📌 {result['title']}")
                print(f"     {result['snippet']}")
        else:
            print("❌ 摘要生成失败")
    
    def show_table_info(self):
        """显示表信息"""
        print("\n📊 表结构信息...")
        
        # 显示表结构
        desc_sql = "DESCRIBE knowledge_base"
        results = self.execute_sql(desc_sql)
        if results:
            print("表结构:")
            for field in results:
                print(f"  {field}")
        
        # 显示记录数
        count_sql = "SELECT COUNT(*) as total FROM knowledge_base"
        results = self.execute_sql(count_sql)
        if results:
            print(f"\n总记录数: {results[0]['total']}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🚀 Manticore Search 简化演示开始")
    print("=" * 50)
    
    # 创建demo实例
    demo = SimpleManticoreDemo()
    
    # 连接数据库
    if not demo.connect():
        print("❌ 无法连接到Manticore Search，请确保服务正在运行")
        sys.exit(1)
    
    try:
        # 执行演示步骤
        if demo.create_demo_table():
            demo.insert_sample_data()
            demo.show_table_info()
            demo.demo_fulltext_search()
            demo.demo_crud_operations()
            demo.demo_snippets()
        
        print("\n🎉 简化演示执行完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示执行出错: {e}")
    finally:
        demo.close()

if __name__ == "__main__":
    main()
