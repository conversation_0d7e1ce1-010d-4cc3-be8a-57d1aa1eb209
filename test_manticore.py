#!/usr/bin/env python3
"""
Manticore Search 简化测试脚本
用于快速验证基本功能
"""

import pymysql
import time
import sys

def test_connection():
    """测试连接"""
    print("🔌 测试 Manticore Search 连接...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=9306,
            user='',
            password='',
            charset='utf8mb4',
            autocommit=True
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"✅ 连接成功！当前表数量: {len(tables)}")
            
            # 显示版本信息
            cursor.execute("SHOW VERSION")
            version = cursor.fetchall()
            if version:
                print(f"📋 Manticore 版本信息:")
                for row in version:
                    print(f"   {row}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_basic_operations():
    """测试基本操作"""
    print("\n🧪 测试基本操作...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=9306,
            user='',
            password='',
            charset='utf8mb4',
            autocommit=True
        )
        
        with connection.cursor() as cursor:
            # 创建测试表
            print("📝 创建测试表...")
            cursor.execute("DROP TABLE IF EXISTS test_table")
            cursor.execute("""
                CREATE TABLE test_table (
                    id BIGINT,
                    title TEXT,
                    content TEXT
                )
            """)
            print("✅ 表创建成功")
            
            # 插入测试数据
            print("📥 插入测试数据...")
            cursor.execute("""
                INSERT INTO test_table (id, title, content) VALUES 
                (1, 'Test Document 1', 'This is a test document for Manticore Search'),
                (2, 'Test Document 2', 'Another test document with different content')
            """)
            print("✅ 数据插入成功")
            
            # 查询数据
            print("🔍 查询数据...")
            cursor.execute("SELECT * FROM test_table")
            results = cursor.fetchall()
            print(f"✅ 查询成功，返回 {len(results)} 条记录")
            
            # 全文搜索
            print("🔎 测试全文搜索...")
            cursor.execute("SELECT * FROM test_table WHERE MATCH('test document')")
            results = cursor.fetchall()
            print(f"✅ 全文搜索成功，找到 {len(results)} 条匹配记录")
            
            # 清理
            cursor.execute("DROP TABLE test_table")
            print("🗑️  清理完成")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def wait_for_manticore(max_attempts=30):
    """等待 Manticore 启动"""
    print("⏳ 等待 Manticore Search 启动...")

    for attempt in range(max_attempts):
        try:
            connection = pymysql.connect(
                host='localhost',
                port=9306,
                user='',
                password='',
                connect_timeout=2
            )
            connection.close()
            print("✅ Manticore Search 已就绪")
            return True
        except Exception as e:
            print(f"   尝试 {attempt + 1}/{max_attempts}... 错误: {e}")
            time.sleep(2)

    print("❌ Manticore Search 启动超时")
    return False

def main():
    """主函数"""
    print("🚀 Manticore Search 快速测试")
    print("=" * 40)
    
    # 等待服务启动
    if not wait_for_manticore():
        sys.exit(1)
    
    # 测试连接
    if not test_connection():
        sys.exit(1)
    
    # 测试基本操作
    if not test_basic_operations():
        sys.exit(1)
    
    print("\n🎉 所有测试通过！")

if __name__ == "__main__":
    main()
