#!/bin/bash

# Manticore Search Demo 运行脚本

set -e

echo "🚀 Manticore Search Demo 启动脚本"
echo "=================================="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装 Python3"
    exit 1
fi

echo "✅ 环境检查通过"

# 创建日志目录
echo "📁 创建必要目录..."
mkdir -p logs

# 停止可能存在的容器
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 启动 Manticore Search
echo "🐳 启动 Manticore Search 容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查容器状态
if ! docker-compose ps | grep -q "Up"; then
    echo "❌ 容器启动失败"
    docker-compose logs
    exit 1
fi

echo "✅ Manticore Search 容器启动成功"

# 安装 Python 依赖
echo "📦 安装 Python 依赖..."
if [ -f "requirements.txt" ]; then
    python3 -m pip install -r requirements.txt --quiet
    echo "✅ 依赖安装完成"
else
    echo "⚠️  requirements.txt 不存在，手动安装依赖..."
    python3 -m pip install pymysql numpy requests --quiet
fi

# 运行快速测试
echo "🧪 运行快速测试..."
python3 test_manticore.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎯 快速测试通过！现在运行完整演示..."
    echo ""
    
    # 运行完整演示
    python3 manticore_demo.py
    
    echo ""
    echo "🎉 演示完成！"
    echo ""
    echo "📋 有用的命令："
    echo "   查看容器状态: docker-compose ps"
    echo "   查看日志:     docker-compose logs"
    echo "   连接数据库:   mysql -h127.0.0.1 -P9306"
    echo "   停止服务:     docker-compose down"
    echo ""
else
    echo "❌ 快速测试失败"
    echo "📋 调试信息："
    echo "   容器状态: docker-compose ps"
    echo "   容器日志: docker-compose logs"
    exit 1
fi
