"""
文档数据模型

定义文档相关的 Pydantic 模型
"""

from typing import Optional, List, Any, Dict
from datetime import datetime
from pydantic import BaseModel, Field, validator
import numpy as np


class DocumentBase(BaseModel):
    """文档基础模型"""
    title: str = Field(..., description="文档标题", max_length=500)
    content: str = Field(..., description="文档内容", max_length=50000)
    category: Optional[str] = Field(None, description="文档分类", max_length=100)
    
    @validator('title', 'content')
    def validate_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('标题和内容不能为空')
        return v.strip()


class DocumentCreate(DocumentBase):
    """创建文档请求模型"""
    embedding: Optional[List[float]] = Field(None, description="文档向量")
    
    @validator('embedding')
    def validate_embedding(cls, v):
        if v is not None:
            if len(v) != 128:  # 默认向量维度
                raise ValueError('向量维度必须为 128')
            # 检查是否为有效的浮点数
            try:
                np.array(v, dtype=np.float32)
            except (ValueError, TypeError):
                raise ValueError('向量必须为有效的浮点数列表')
        return v


class DocumentUpdate(BaseModel):
    """更新文档请求模型"""
    title: Optional[str] = Field(None, description="文档标题", max_length=500)
    content: Optional[str] = Field(None, description="文档内容", max_length=50000)
    category: Optional[str] = Field(None, description="文档分类", max_length=100)
    embedding: Optional[List[float]] = Field(None, description="文档向量")
    
    @validator('title', 'content', 'category')
    def validate_not_empty_if_provided(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('如果提供值，则不能为空')
        return v.strip() if v else v
    
    @validator('embedding')
    def validate_embedding(cls, v):
        if v is not None:
            if len(v) != 128:
                raise ValueError('向量维度必须为 128')
            try:
                np.array(v, dtype=np.float32)
            except (ValueError, TypeError):
                raise ValueError('向量必须为有效的浮点数列表')
        return v


class Document(DocumentBase):
    """完整文档模型"""
    id: int = Field(..., description="文档ID")
    embedding: Optional[List[float]] = Field(None, description="文档向量")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class DocumentWithScore(Document):
    """带评分的文档模型（用于搜索结果）"""
    score: float = Field(..., description="相关性评分")
    snippet: Optional[str] = Field(None, description="高亮摘要")
    
    class Config:
        from_attributes = True


class BulkDocumentCreate(BaseModel):
    """批量创建文档请求模型"""
    documents: List[DocumentCreate] = Field(..., description="文档列表")
    
    @validator('documents')
    def validate_documents_not_empty(cls, v):
        if not v:
            raise ValueError('文档列表不能为空')
        if len(v) > 1000:  # 限制批量操作数量
            raise ValueError('单次批量操作不能超过 1000 个文档')
        return v


class BulkDocumentUpdate(BaseModel):
    """批量更新文档请求模型"""
    updates: List[Dict[str, Any]] = Field(..., description="更新操作列表")
    
    @validator('updates')
    def validate_updates_not_empty(cls, v):
        if not v:
            raise ValueError('更新列表不能为空')
        if len(v) > 1000:
            raise ValueError('单次批量操作不能超过 1000 个文档')
        
        # 验证每个更新操作都有 id
        for update in v:
            if 'id' not in update:
                raise ValueError('每个更新操作都必须包含文档 ID')
        return v


class DocumentStats(BaseModel):
    """文档统计信息模型"""
    total_documents: int = Field(..., description="文档总数")
    total_categories: int = Field(..., description="分类总数")
    avg_content_length: float = Field(..., description="平均内容长度")
    latest_document_time: Optional[datetime] = Field(None, description="最新文档时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


# 工具函数
def create_sample_embedding(text: str, dimensions: int = 128) -> List[float]:
    """创建示例向量（用于测试）"""
    import hashlib
    import struct
    
    # 使用文本的哈希值生成确定性的向量
    hash_obj = hashlib.md5(text.encode())
    hash_bytes = hash_obj.digest()
    
    # 将哈希值转换为浮点数向量
    vector = []
    for i in range(dimensions):
        byte_idx = i % len(hash_bytes)
        value = struct.unpack('B', hash_bytes[byte_idx:byte_idx+1])[0]
        # 归一化到 [-1, 1] 范围
        normalized_value = (value - 127.5) / 127.5
        vector.append(normalized_value)
    
    return vector


if __name__ == "__main__":
    # 测试模型
    doc_create = DocumentCreate(
        title="测试文档",
        content="这是一个测试文档的内容",
        category="测试",
        embedding=create_sample_embedding("测试文档")
    )
    
    print("创建文档模型:")
    print(doc_create.json(indent=2, ensure_ascii=False))
    
    # 测试向量生成
    embedding = create_sample_embedding("测试文本")
    print(f"\n生成的向量维度: {len(embedding)}")
    print(f"向量前5个值: {embedding[:5]}")
