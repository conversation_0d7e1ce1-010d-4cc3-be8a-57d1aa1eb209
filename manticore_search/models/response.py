"""
通用响应数据模型

定义 API 响应的标准格式
"""

from typing import Optional, Any, Dict, List, Generic, TypeVar
from datetime import datetime
from pydantic import BaseModel, Field

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """通用 API 响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @classmethod
    def success_response(
        cls, 
        data: Optional[T] = None, 
        message: str = "操作成功"
    ) -> "ApiResponse[T]":
        """创建成功响应"""
        return cls(
            success=True,
            message=message,
            data=data
        )
    
    @classmethod
    def error_response(
        cls,
        message: str = "操作失败",
        error_code: Optional[str] = None,
        data: Optional[T] = None
    ) -> "ApiResponse[T]":
        """创建错误响应"""
        return cls(
            success=False,
            message=message,
            error_code=error_code,
            data=data
        )


class HealthStatus(BaseModel):
    """健康状态模型"""
    status: str = Field(..., description="服务状态")
    database_connected: bool = Field(..., description="数据库连接状态")
    response_time_ms: float = Field(..., description="响应时间(毫秒)")
    version: str = Field(..., description="服务版本")
    uptime_seconds: float = Field(..., description="运行时间(秒)")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="整体状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    services: Dict[str, HealthStatus] = Field(..., description="各服务状态")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int = Field(..., description="当前页码", ge=1)
    page_size: int = Field(..., description="每页大小", ge=1, le=100)
    total_items: int = Field(..., description="总项目数", ge=0)
    total_pages: int = Field(..., description="总页数", ge=0)
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
    
    @classmethod
    def create(
        cls,
        page: int,
        page_size: int,
        total_items: int
    ) -> "PaginationInfo":
        """创建分页信息"""
        total_pages = (total_items + page_size - 1) // page_size
        return cls(
            page=page,
            page_size=page_size,
            total_items=total_items,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    items: List[T] = Field(..., description="数据项列表")
    pagination: PaginationInfo = Field(..., description="分页信息")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BatchOperationResult(BaseModel):
    """批量操作结果模型"""
    total: int = Field(..., description="总操作数")
    success: int = Field(..., description="成功数")
    failed: int = Field(..., description="失败数")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="错误详情")
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.success / self.total if self.total > 0 else 0.0


class OperationStatus(BaseModel):
    """操作状态模型"""
    operation_id: str = Field(..., description="操作ID")
    status: str = Field(..., description="操作状态")
    progress: float = Field(..., description="进度百分比", ge=0.0, le=100.0)
    message: str = Field(..., description="状态消息")
    started_at: datetime = Field(..., description="开始时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ValidationErrorDetail(BaseModel):
    """验证错误详情模型"""
    field: str = Field(..., description="字段名")
    message: str = Field(..., description="错误消息")
    invalid_value: Any = Field(..., description="无效值")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    validation_errors: Optional[List[ValidationErrorDetail]] = Field(None, description="验证错误")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MetricsResponse(BaseModel):
    """指标响应模型"""
    metrics: Dict[str, Any] = Field(..., description="指标数据")
    collected_at: datetime = Field(default_factory=datetime.now, description="收集时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# 常用响应类型别名
DocumentResponse = ApiResponse[Dict[str, Any]]
DocumentListResponse = PaginatedResponse[Dict[str, Any]]
SearchResultResponse = ApiResponse[Dict[str, Any]]
BatchResponse = ApiResponse[BatchOperationResult]


# 工具函数
def create_success_response(data: Any = None, message: str = "操作成功") -> ApiResponse:
    """创建成功响应的便捷函数"""
    return ApiResponse.success_response(data=data, message=message)


def create_error_response(
    message: str = "操作失败",
    error_code: Optional[str] = None
) -> ApiResponse:
    """创建错误响应的便捷函数"""
    return ApiResponse.error_response(message=message, error_code=error_code)


def create_validation_error_response(
    validation_errors: List[ValidationErrorDetail]
) -> ErrorResponse:
    """创建验证错误响应"""
    return ErrorResponse(
        error="ValidationError",
        message="请求数据验证失败",
        error_code="VALIDATION_ERROR",
        validation_errors=validation_errors
    )


if __name__ == "__main__":
    # 测试响应模型
    
    # 测试成功响应
    success_resp = create_success_response(
        data={"id": 1, "title": "测试文档"},
        message="文档创建成功"
    )
    print("成功响应:")
    print(success_resp.json(indent=2, ensure_ascii=False))
    
    # 测试分页信息
    pagination = PaginationInfo.create(page=1, page_size=20, total_items=100)
    print("\n分页信息:")
    print(pagination.json(indent=2, ensure_ascii=False))
    
    # 测试健康状态
    health_status = HealthStatus(
        status="healthy",
        database_connected=True,
        response_time_ms=15.5,
        version="1.0.0",
        uptime_seconds=3600.0
    )
    print("\n健康状态:")
    print(health_status.json(indent=2, ensure_ascii=False))
