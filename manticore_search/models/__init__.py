"""
数据模型模块

定义所有 Pydantic 数据模型
"""

from .document import (
    DocumentBase,
    DocumentCreate,
    DocumentUpdate,
    Document,
    DocumentWithScore,
    BulkDocumentCreate,
    BulkDocumentUpdate,
    DocumentStats,
    create_sample_embedding
)

from .search import (
    SearchType,
    SortOrder,
    SearchRequest,
    SearchResult,
    SearchResponse,
    SuggestRequest,
    SuggestResponse,
    SearchStats,
    AdvancedSearchRequest,
    create_simple_search_request,
    create_vector_search_request,
    create_hybrid_search_request
)

from .response import (
    ApiResponse,
    HealthStatus,
    HealthResponse,
    PaginationInfo,
    PaginatedResponse,
    BatchOperationResult,
    OperationStatus,
    ValidationErrorDetail,
    ErrorResponse,
    MetricsResponse,
    DocumentResponse,
    DocumentListResponse,
    SearchResultResponse,
    BatchResponse,
    create_success_response,
    create_error_response,
    create_validation_error_response
)

__all__ = [
    # 文档模型
    "DocumentBase",
    "DocumentCreate",
    "DocumentUpdate",
    "Document",
    "DocumentWithScore",
    "BulkDocumentCreate",
    "BulkDocumentUpdate",
    "DocumentStats",
    "create_sample_embedding",
    
    # 搜索模型
    "SearchType",
    "SortOrder",
    "SearchRequest",
    "SearchResult",
    "SearchResponse",
    "SuggestRequest",
    "SuggestResponse",
    "SearchStats",
    "AdvancedSearchRequest",
    "create_simple_search_request",
    "create_vector_search_request",
    "create_hybrid_search_request",
    
    # 响应模型
    "ApiResponse",
    "HealthStatus",
    "HealthResponse",
    "PaginationInfo",
    "PaginatedResponse",
    "BatchOperationResult",
    "OperationStatus",
    "ValidationErrorDetail",
    "ErrorResponse",
    "MetricsResponse",
    "DocumentResponse",
    "DocumentListResponse",
    "SearchResultResponse",
    "BatchResponse",
    "create_success_response",
    "create_error_response",
    "create_validation_error_response",
]
