"""
文档服务测试

测试文档管理服务的各项功能
"""

import pytest
import time
from ..services import DocumentService
from ..models import DocumentCreate, DocumentUpdate, BulkDocumentCreate
from ..utils import get_settings, DocumentNotFoundError


class TestDocumentService:
    """文档服务测试类"""
    
    @pytest.fixture(scope="class")
    def service(self):
        """创建文档服务实例"""
        settings = get_settings()
        return DocumentService(settings)
    
    @pytest.fixture
    def sample_document(self):
        """创建示例文档"""
        return DocumentCreate(
            title="测试文档",
            content="这是一个用于测试的文档内容，包含了各种测试数据。",
            category="测试"
        )
    
    def test_create_document(self, service, sample_document):
        """测试创建文档"""
        # 创建文档
        document = service.create_document(sample_document)
        
        # 验证结果
        assert document.id is not None
        assert document.title == sample_document.title
        assert document.content == sample_document.content
        assert document.category == sample_document.category
        assert document.embedding is not None
        assert len(document.embedding) == 128
        
        # 清理：删除创建的文档
        service.delete_document(document.id)
    
    def test_get_document(self, service, sample_document):
        """测试获取文档"""
        # 先创建文档
        created_doc = service.create_document(sample_document)
        
        # 获取文档
        retrieved_doc = service.get_document(created_doc.id)
        
        # 验证结果
        assert retrieved_doc.id == created_doc.id
        assert retrieved_doc.title == created_doc.title
        assert retrieved_doc.content == created_doc.content
        assert retrieved_doc.category == created_doc.category
        
        # 清理
        service.delete_document(created_doc.id)
    
    def test_get_nonexistent_document(self, service):
        """测试获取不存在的文档"""
        with pytest.raises(DocumentNotFoundError):
            service.get_document(999999999)
    
    def test_update_document(self, service, sample_document):
        """测试更新文档"""
        # 先创建文档
        created_doc = service.create_document(sample_document)
        
        # 更新文档
        update_data = DocumentUpdate(
            title="更新后的标题",
            content="更新后的内容"
        )
        
        updated_doc = service.update_document(created_doc.id, update_data)
        
        # 验证结果
        assert updated_doc.id == created_doc.id
        assert updated_doc.title == "更新后的标题"
        assert updated_doc.content == "更新后的内容"
        assert updated_doc.category == created_doc.category  # 未更新的字段保持不变
        
        # 清理
        service.delete_document(created_doc.id)
    
    def test_delete_document(self, service, sample_document):
        """测试删除文档"""
        # 先创建文档
        created_doc = service.create_document(sample_document)
        
        # 删除文档
        success = service.delete_document(created_doc.id)
        assert success is True
        
        # 验证文档已被删除
        with pytest.raises(DocumentNotFoundError):
            service.get_document(created_doc.id)
    
    def test_list_documents(self, service):
        """测试列出文档"""
        # 创建几个测试文档
        test_docs = []
        for i in range(3):
            doc_data = DocumentCreate(
                title=f"测试文档 {i}",
                content=f"这是第 {i} 个测试文档的内容",
                category="测试列表"
            )
            doc = service.create_document(doc_data)
            test_docs.append(doc)
        
        try:
            # 列出文档
            documents = service.list_documents(limit=10, offset=0, category="测试列表")
            
            # 验证结果
            assert len(documents) >= 3
            
            # 验证包含我们创建的文档
            doc_ids = [doc.id for doc in documents]
            for test_doc in test_docs:
                assert test_doc.id in doc_ids
        
        finally:
            # 清理
            for doc in test_docs:
                try:
                    service.delete_document(doc.id)
                except:
                    pass
    
    def test_bulk_create_documents(self, service):
        """测试批量创建文档"""
        # 准备批量数据
        documents = [
            DocumentCreate(
                title=f"批量文档 {i}",
                content=f"这是第 {i} 个批量创建的文档",
                category="批量测试"
            )
            for i in range(5)
        ]
        
        bulk_data = BulkDocumentCreate(documents=documents)
        
        # 批量创建
        result = service.bulk_create_documents(bulk_data)
        
        # 验证结果
        assert result['total'] == 5
        assert result['success'] == 5
        assert result['failed'] == 0
        
        # 验证文档确实被创建
        created_docs = service.list_documents(limit=10, category="批量测试")
        assert len(created_docs) >= 5
        
        # 清理
        for doc in created_docs:
            if doc.category == "批量测试":
                try:
                    service.delete_document(doc.id)
                except:
                    pass
    
    def test_get_document_stats(self, service):
        """测试获取文档统计"""
        # 创建一些测试文档
        test_docs = []
        for i in range(3):
            doc_data = DocumentCreate(
                title=f"统计测试文档 {i}",
                content=f"这是用于统计测试的文档内容 {i}",
                category=f"统计分类{i % 2}"  # 创建2个不同分类
            )
            doc = service.create_document(doc_data)
            test_docs.append(doc)
        
        try:
            # 获取统计信息
            stats = service.get_document_stats()
            
            # 验证结果
            assert stats.total_documents >= 3
            assert stats.total_categories >= 2
            assert stats.avg_content_length > 0
        
        finally:
            # 清理
            for doc in test_docs:
                try:
                    service.delete_document(doc.id)
                except:
                    pass


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
