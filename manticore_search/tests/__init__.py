"""
测试模块

包含所有测试用例
"""

# 测试配置
import os
import sys

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 测试环境配置
os.environ.setdefault("MANTICORE_HOST", "localhost")
os.environ.setdefault("MANTICORE_PORT", "9306")
os.environ.setdefault("MANTICORE_LOG_LEVEL", "WARNING")  # 减少测试时的日志输出
