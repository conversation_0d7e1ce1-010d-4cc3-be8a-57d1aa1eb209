"""
搜索服务测试

测试搜索服务的各项功能
"""

import pytest
from ..services import SearchService, DocumentService
from ..models import (
    DocumentCreate, 
    SearchRequest, 
    SearchType,
    create_simple_search_request,
    create_vector_search_request,
    create_sample_embedding
)
from ..utils import get_settings


class TestSearchService:
    """搜索服务测试类"""
    
    @pytest.fixture(scope="class")
    def search_service(self):
        """创建搜索服务实例"""
        settings = get_settings()
        return SearchService(settings)
    
    @pytest.fixture(scope="class")
    def document_service(self):
        """创建文档服务实例"""
        settings = get_settings()
        return DocumentService(settings)
    
    @pytest.fixture(scope="class")
    def test_documents(self, document_service):
        """创建测试文档"""
        test_docs = []
        
        # 创建一些测试文档
        documents_data = [
            {
                "title": "Python 编程指南",
                "content": "Python 是一种高级编程语言，广泛用于数据科学、机器学习和Web开发。",
                "category": "编程"
            },
            {
                "title": "机器学习基础",
                "content": "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习。",
                "category": "AI"
            },
            {
                "title": "数据库设计原理",
                "content": "数据库设计需要考虑数据结构、索引优化和查询性能等因素。",
                "category": "数据库"
            },
            {
                "title": "搜索引擎技术",
                "content": "搜索引擎使用倒排索引、相关性评分等技术来快速检索信息。",
                "category": "搜索"
            },
            {
                "title": "向量搜索原理",
                "content": "向量搜索通过计算向量之间的相似度来找到最相关的文档。",
                "category": "搜索"
            }
        ]
        
        for doc_data in documents_data:
            doc_create = DocumentCreate(**doc_data)
            doc = document_service.create_document(doc_create)
            test_docs.append(doc)
        
        yield test_docs
        
        # 清理测试数据
        for doc in test_docs:
            try:
                document_service.delete_document(doc.id)
            except:
                pass
    
    def test_fulltext_search(self, search_service, test_documents):
        """测试全文搜索"""
        # 创建搜索请求
        request = create_simple_search_request("Python 编程", 10)
        
        # 执行搜索
        response = search_service.search(request)
        
        # 验证结果
        assert response.total > 0
        assert response.search_type == SearchType.FULLTEXT
        assert response.query == "Python 编程"
        assert response.took > 0
        assert len(response.results) > 0
        
        # 验证结果包含相关文档
        found_python_doc = False
        for result in response.results:
            if "Python" in result.document.title or "Python" in result.document.content:
                found_python_doc = True
                assert result.document.score > 0
                break
        
        assert found_python_doc, "应该找到包含 Python 的文档"
    
    def test_search_with_category_filter(self, search_service, test_documents):
        """测试带分类过滤的搜索"""
        # 创建带分类过滤的搜索请求
        request = SearchRequest(
            query="搜索",
            search_type=SearchType.FULLTEXT,
            category="搜索",
            limit=10
        )
        
        # 执行搜索
        response = search_service.search(request)
        
        # 验证结果
        assert response.total > 0
        
        # 验证所有结果都属于指定分类
        for result in response.results:
            assert result.document.category == "搜索"
    
    def test_vector_search(self, search_service, test_documents):
        """测试向量搜索"""
        # 生成查询向量
        query_vector = create_sample_embedding("机器学习算法")
        
        # 创建向量搜索请求
        request = create_vector_search_request("机器学习", query_vector, 5)
        
        # 执行搜索
        response = search_service.search(request)
        
        # 验证结果
        assert response.search_type == SearchType.VECTOR
        assert response.query == "机器学习"
        assert len(response.results) > 0
        
        # 验证结果有相关性分数
        for result in response.results:
            assert result.document.score is not None
    
    def test_hybrid_search(self, search_service, test_documents):
        """测试混合搜索"""
        # 生成查询向量
        query_vector = create_sample_embedding("数据库索引优化")
        
        # 创建混合搜索请求
        request = SearchRequest(
            query="数据库",
            search_type=SearchType.HYBRID,
            query_vector=query_vector,
            vector_weight=0.3,
            limit=10
        )
        
        # 执行搜索
        response = search_service.search(request)
        
        # 验证结果
        assert response.search_type == SearchType.HYBRID
        assert response.query == "数据库"
        assert len(response.results) > 0
        
        # 验证混合搜索的分数计算
        for result in response.results:
            assert result.document.score is not None
            assert result.document.score >= 0
    
    def test_search_with_pagination(self, search_service, test_documents):
        """测试搜索分页"""
        # 第一页
        request1 = SearchRequest(
            query="学习",
            limit=2,
            offset=0
        )
        response1 = search_service.search(request1)
        
        # 第二页
        request2 = SearchRequest(
            query="学习",
            limit=2,
            offset=2
        )
        response2 = search_service.search(request2)
        
        # 验证分页结果
        assert response1.limit == 2
        assert response1.offset == 0
        assert response2.limit == 2
        assert response2.offset == 2
        
        # 验证不同页的结果不同（如果有足够的结果）
        if len(response1.results) > 0 and len(response2.results) > 0:
            result1_ids = {r.document.id for r in response1.results}
            result2_ids = {r.document.id for r in response2.results}
            assert result1_ids != result2_ids, "不同页的结果应该不同"
    
    def test_search_suggestions(self, search_service, test_documents):
        """测试搜索建议"""
        # 获取搜索建议
        suggestions = search_service.suggest("Python", 5)
        
        # 验证结果
        assert isinstance(suggestions, list)
        # 建议可能为空，这是正常的
        
        # 如果有建议，验证格式
        for suggestion in suggestions:
            assert isinstance(suggestion, str)
            assert len(suggestion) > 0
    
    def test_search_with_min_score(self, search_service, test_documents):
        """测试最小分数过滤"""
        # 创建带最小分数的搜索请求
        request = SearchRequest(
            query="编程",
            min_score=0.1,
            limit=10
        )
        
        # 执行搜索
        response = search_service.search(request)
        
        # 验证所有结果的分数都大于等于最小分数
        for result in response.results:
            assert result.document.score >= 0.1
    
    def test_search_empty_query(self, search_service):
        """测试空查询"""
        with pytest.raises(Exception):  # 应该抛出验证错误
            request = SearchRequest(query="")
            search_service.search(request)
    
    def test_search_statistics(self, search_service, test_documents):
        """测试搜索统计信息"""
        # 执行搜索
        request = create_simple_search_request("技术", 10)
        response = search_service.search(request)
        
        # 验证统计信息
        if response.results:
            assert response.max_score is not None
            assert response.min_score is not None
            assert response.avg_score is not None
            assert response.max_score >= response.min_score
            assert response.avg_score >= response.min_score
            assert response.avg_score <= response.max_score


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
