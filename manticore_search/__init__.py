"""
Manticore Search 高内聚模块

这是一个基于 BMAD 工程规范的 Manticore Search 模块，提供：
- 高内聚的搜索服务
- FastAPI 接口层
- 完整的数据访问层
- 统一的配置管理
- 全面的测试覆盖

Author: <PERSON> (Architect)
Version: 1.0.0
"""

from .clients.manticore_client import ManticoreClient
from .services.search_service import SearchService
from .services.document_service import DocumentService
from .services.health_service import HealthService
from .models.document import Document, DocumentCreate, DocumentUpdate, create_sample_embedding
from .models.search import (
    SearchRequest, SearchResponse, SearchResult,
    create_simple_search_request, create_vector_search_request, create_hybrid_search_request
)
from .models.response import ApiResponse, HealthResponse
from .utils.config import Settings, get_settings
from .utils.logger import get_logger

__version__ = "1.0.0"
__author__ = "<PERSON> (Architect)"

# 导出主要类和函数
__all__ = [
    # 客户端
    "ManticoreClient",
    
    # 服务层
    "SearchService",
    "DocumentService", 
    "HealthService",
    
    # 数据模型
    "Document",
    "DocumentCreate",
    "DocumentUpdate",
    "create_sample_embedding",
    "SearchRequest",
    "SearchResponse",
    "SearchResult",
    "create_simple_search_request",
    "create_vector_search_request",
    "create_hybrid_search_request",
    "ApiResponse",
    "HealthResponse",
    
    # 工具
    "Settings",
    "get_settings",
    "get_logger",
]
