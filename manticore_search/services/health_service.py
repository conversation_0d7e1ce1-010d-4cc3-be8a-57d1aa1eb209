"""
健康检查服务

提供系统健康状态检查和监控功能
"""

import time
import psutil
from typing import Dict, Any
from ..clients import ManticoreClient
from ..models import HealthStatus, HealthResponse
from ..utils import get_service_logger, Settings


class HealthService:
    """健康检查服务"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = ManticoreClient(settings)
        self.logger = get_service_logger()
        self.start_time = time.time()
    
    async def get_health_status(self) -> HealthResponse:
        """获取完整的健康状态"""
        try:
            # 检查各个服务的健康状态
            services = {
                "manticore": await self._check_manticore_health(),
                "system": self._check_system_health(),
                "application": self._check_application_health()
            }
            
            # 确定整体状态
            overall_status = "healthy"
            for service_status in services.values():
                if service_status.status != "healthy":
                    overall_status = "unhealthy"
                    break
            
            return HealthResponse(
                status=overall_status,
                services=services
            )
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return HealthResponse(
                status="error",
                services={
                    "error": HealthStatus(
                        status="error",
                        database_connected=False,
                        response_time_ms=0.0,
                        version="unknown",
                        uptime_seconds=0.0
                    )
                }
            )
    
    async def _check_manticore_health(self) -> HealthStatus:
        """检查 Manticore Search 健康状态"""
        start_time = time.time()
        
        try:
            # 测试连接
            connected = self.client.test_connection()
            response_time = (time.time() - start_time) * 1000
            
            if connected:
                # 获取版本信息
                server_info = self.client.get_server_info()
                version = server_info.get("version", "unknown")
                
                return HealthStatus(
                    status="healthy",
                    database_connected=True,
                    response_time_ms=response_time,
                    version=version,
                    uptime_seconds=time.time() - self.start_time
                )
            else:
                return HealthStatus(
                    status="unhealthy",
                    database_connected=False,
                    response_time_ms=response_time,
                    version="unknown",
                    uptime_seconds=time.time() - self.start_time
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            self.logger.error(f"Manticore 健康检查失败: {e}")
            
            return HealthStatus(
                status="error",
                database_connected=False,
                response_time_ms=response_time,
                version="unknown",
                uptime_seconds=time.time() - self.start_time
            )
    
    def _check_system_health(self) -> HealthStatus:
        """检查系统健康状态"""
        try:
            # 获取系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 判断系统状态
            status = "healthy"
            if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
                status = "warning"
            
            return HealthStatus(
                status=status,
                database_connected=True,  # 系统检查不涉及数据库
                response_time_ms=0.0,
                version=f"CPU: {cpu_percent:.1f}%, Memory: {memory.percent:.1f}%, Disk: {disk.percent:.1f}%",
                uptime_seconds=time.time() - psutil.boot_time()
            )
            
        except Exception as e:
            self.logger.error(f"系统健康检查失败: {e}")
            return HealthStatus(
                status="error",
                database_connected=False,
                response_time_ms=0.0,
                version="unknown",
                uptime_seconds=0.0
            )
    
    def _check_application_health(self) -> HealthStatus:
        """检查应用程序健康状态"""
        try:
            # 检查应用程序状态
            uptime = time.time() - self.start_time
            
            # 简单的应用健康检查
            status = "healthy"
            
            return HealthStatus(
                status=status,
                database_connected=True,
                response_time_ms=0.0,
                version="1.0.0",
                uptime_seconds=uptime
            )
            
        except Exception as e:
            self.logger.error(f"应用程序健康检查失败: {e}")
            return HealthStatus(
                status="error",
                database_connected=False,
                response_time_ms=0.0,
                version="unknown",
                uptime_seconds=0.0
            )
    
    async def check_connection(self) -> bool:
        """简单的连接检查"""
        try:
            return self.client.test_connection()
        except Exception as e:
            self.logger.error(f"连接检查失败: {e}")
            return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # 获取系统指标
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 获取应用指标
            uptime = time.time() - self.start_time
            
            return {
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_used_gb": memory.used / (1024**3),
                    "memory_total_gb": memory.total / (1024**3),
                    "disk_percent": disk.percent,
                    "disk_used_gb": disk.used / (1024**3),
                    "disk_total_gb": disk.total / (1024**3)
                },
                "application": {
                    "uptime_seconds": uptime,
                    "uptime_hours": uptime / 3600,
                    "version": "1.0.0"
                },
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.logger.error(f"获取系统指标失败: {e}")
            return {"error": str(e), "timestamp": time.time()}


if __name__ == "__main__":
    # 测试健康检查服务
    import asyncio
    from ..utils import get_settings
    
    async def test_health_service():
        settings = get_settings()
        service = HealthService(settings)
        
        # 测试健康检查
        health_response = await service.get_health_status()
        print(f"整体状态: {health_response.status}")
        
        for service_name, service_status in health_response.services.items():
            print(f"{service_name}: {service_status.status}")
            if service_name == "manticore":
                print(f"  - 数据库连接: {service_status.database_connected}")
                print(f"  - 响应时间: {service_status.response_time_ms:.2f}ms")
                print(f"  - 版本: {service_status.version}")
        
        # 测试指标获取
        metrics = service.get_metrics()
        print(f"\n系统指标:")
        print(f"CPU: {metrics['system']['cpu_percent']:.1f}%")
        print(f"内存: {metrics['system']['memory_percent']:.1f}%")
        print(f"磁盘: {metrics['system']['disk_percent']:.1f}%")
    
    # 运行测试
    asyncio.run(test_health_service())
