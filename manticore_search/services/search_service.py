"""
搜索服务

提供全文搜索、向量搜索和混合搜索功能
"""

import time
import re
from typing import Optional, List, Dict, Any
from ..clients import ManticoreClient
from ..models import (
    SearchRequest,
    SearchResponse,
    SearchResult,
    SearchType,
    DocumentWithScore,
    create_sample_embedding
)
from ..utils import (
    get_service_logger,
    Settings,
    SearchError,
    VectorSearchError,
    ValidationError
)


class SearchService:
    """搜索服务"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = ManticoreClient(settings)
        self.logger = get_service_logger()
        self.table_name = settings.default_table_name
    
    def search(self, request: SearchRequest) -> SearchResponse:
        """执行搜索"""
        start_time = time.time()
        
        try:
            if request.search_type == SearchType.FULLTEXT:
                results = self._fulltext_search(request)
            elif request.search_type == SearchType.VECTOR:
                results = self._vector_search(request)
            elif request.search_type == SearchType.HYBRID:
                results = self._hybrid_search(request)
            else:
                raise ValidationError(f"不支持的搜索类型: {request.search_type}")
            
            # 计算搜索耗时
            took = (time.time() - start_time) * 1000
            
            # 构建响应
            search_results = []
            scores = []
            
            for row in results:
                score = float(row.get('relevance_score', 0))
                scores.append(score)
                
                # 创建文档对象
                document = DocumentWithScore(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    category=row.get('category', ''),
                    score=score,
                    snippet=row.get('snippet', None),
                    embedding=None  # 不返回向量以节省带宽
                )
                
                search_results.append(SearchResult(
                    document=document,
                    match_info=self._extract_match_info(row)
                ))
            
            # 计算统计信息
            max_score = max(scores) if scores else 0.0
            min_score = min(scores) if scores else 0.0
            avg_score = sum(scores) / len(scores) if scores else 0.0
            
            return SearchResponse(
                results=search_results,
                total=len(results),
                took=took,
                limit=request.limit,
                offset=request.offset,
                has_more=len(results) == request.limit,  # 简化的判断
                search_type=request.search_type,
                query=request.query,
                max_score=max_score,
                min_score=min_score,
                avg_score=avg_score
            )
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            raise SearchError(f"搜索执行失败: {e}")
    
    def _fulltext_search(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """全文搜索"""
        try:
            # 构建基础查询
            sql = f"""
            SELECT *, WEIGHT() as relevance_score
            FROM {self.table_name}
            WHERE MATCH(%s)
            """
            params = [request.query]
            
            # 添加分类过滤
            if request.category:
                sql += " AND category = %s"
                params.append(request.category)
            
            # 添加最小分数过滤
            if request.min_score:
                sql += " AND WEIGHT() >= %s"
                params.append(request.min_score)
            
            # 添加排序
            sql += " ORDER BY relevance_score DESC"
            
            # 添加分页
            sql += f" LIMIT {request.offset}, {request.limit}"
            
            # 执行查询
            results = self.client.execute_query(sql, tuple(params))
            
            # 如果启用摘要，添加高亮摘要
            if request.enable_snippet and results:
                results = self._add_snippets(results, request)
            
            return results or []
            
        except Exception as e:
            self.logger.error(f"全文搜索失败: {e}")
            raise SearchError(f"全文搜索失败: {e}")
    
    def _vector_search(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """向量搜索"""
        try:
            if not request.query_vector:
                raise VectorSearchError("向量搜索需要提供查询向量")

            # 构建 KNN 查询向量
            vector_str = '(' + ','.join(map(str, request.query_vector)) + ')'

            # 使用正确的 KNN 语法
            sql = f"""
            SELECT *, KNN_DIST() as relevance_score
            FROM {self.table_name}
            WHERE KNN('embedding', {vector_str}, {request.limit})
            """

            params = []

            # 添加分类过滤（KNN 查询中的额外过滤）
            if request.category:
                sql += " AND category = %s"
                params.append(request.category)

            # KNN 查询已经按距离排序，不需要额外的 ORDER BY

            # 执行查询
            results = self.client.execute_query(sql, tuple(params) if params else None)

            return results or []

        except Exception as e:
            self.logger.error(f"向量搜索失败: {e}")
            raise VectorSearchError(f"向量搜索失败: {e}")
    
    def _hybrid_search(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """混合搜索（全文 + 向量）"""
        try:
            if not request.query_vector:
                raise VectorSearchError("混合搜索需要提供查询向量")
            
            # 获取全文搜索结果
            fulltext_results = self._fulltext_search(request)
            
            # 获取向量搜索结果
            vector_results = self._vector_search(request)
            
            # 合并和重新评分
            combined_results = self._combine_search_results(
                fulltext_results,
                vector_results,
                request.vector_weight
            )
            
            # 重新排序和分页
            combined_results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            start_idx = request.offset
            end_idx = start_idx + request.limit
            
            return combined_results[start_idx:end_idx]
            
        except Exception as e:
            self.logger.error(f"混合搜索失败: {e}")
            raise SearchError(f"混合搜索失败: {e}")
    
    def _combine_search_results(
        self,
        fulltext_results: List[Dict[str, Any]],
        vector_results: List[Dict[str, Any]],
        vector_weight: float
    ) -> List[Dict[str, Any]]:
        """合并搜索结果"""
        # 创建文档ID到结果的映射
        fulltext_map = {row['id']: row for row in fulltext_results}
        vector_map = {row['id']: row for row in vector_results}
        
        # 获取所有文档ID
        all_doc_ids = set(fulltext_map.keys()) | set(vector_map.keys())
        
        combined_results = []
        
        for doc_id in all_doc_ids:
            fulltext_row = fulltext_map.get(doc_id)
            vector_row = vector_map.get(doc_id)
            
            # 计算组合分数
            fulltext_score = fulltext_row['relevance_score'] if fulltext_row else 0
            vector_score = vector_row['relevance_score'] if vector_row else 0
            
            combined_score = (
                (1 - vector_weight) * fulltext_score +
                vector_weight * vector_score
            )
            
            # 使用全文搜索结果作为基础（包含摘要等信息）
            base_row = fulltext_row or vector_row
            base_row['relevance_score'] = combined_score
            
            combined_results.append(base_row)
        
        return combined_results
    
    def _add_snippets(
        self, 
        results: List[Dict[str, Any]], 
        request: SearchRequest
    ) -> List[Dict[str, Any]]:
        """添加高亮摘要"""
        try:
            # 使用 Manticore 的 SNIPPET 函数
            snippet_length = request.snippet_length or self.settings.snippet_length
            snippet_around = request.snippet_around or self.settings.snippet_around
            
            for row in results:
                # 构建 SNIPPET 查询
                snippet_sql = f"""
                SELECT SNIPPET(content, %s, 'limit={snippet_length}', 'around={snippet_around}') as snippet
                FROM {self.table_name}
                WHERE id = %s
                """
                
                snippet_result = self.client.execute_query(
                    snippet_sql, 
                    (request.query, row['id'])
                )
                
                if snippet_result:
                    row['snippet'] = snippet_result[0]['snippet']
                else:
                    # 如果 SNIPPET 失败，使用简单的文本截取
                    content = row.get('content', '')
                    if len(content) > snippet_length:
                        row['snippet'] = content[:snippet_length] + '...'
                    else:
                        row['snippet'] = content
            
            return results
            
        except Exception as e:
            self.logger.warning(f"添加摘要失败，使用原始内容: {e}")
            # 如果摘要生成失败，返回原始结果
            return results
    
    def _extract_match_info(self, row: Dict[str, Any]) -> Dict[str, Any]:
        """提取匹配信息"""
        return {
            'score': row.get('relevance_score', 0),
            'has_snippet': 'snippet' in row and row['snippet'] is not None
        }
    
    def suggest(self, query: str, limit: int = 10) -> List[str]:
        """搜索建议（简单实现）"""
        try:
            # 基于现有文档标题生成建议
            sql = f"""
            SELECT DISTINCT title
            FROM {self.table_name}
            WHERE title LIKE %s
            LIMIT {limit}
            """
            
            like_pattern = f"%{query}%"
            results = self.client.execute_query(sql, (like_pattern,))
            
            suggestions = [row['title'] for row in results or []]
            return suggestions
            
        except Exception as e:
            self.logger.error(f"生成搜索建议失败: {e}")
            return []


if __name__ == "__main__":
    # 测试搜索服务
    from ..utils import get_settings
    from ..models import create_simple_search_request
    
    settings = get_settings()
    service = SearchService(settings)
    
    # 测试全文搜索
    request = create_simple_search_request("测试", 5)
    
    try:
        response = service.search(request)
        print(f"✅ 搜索成功: 找到 {response.total} 个结果")
        print(f"搜索耗时: {response.took:.2f}ms")
        
        for result in response.results:
            print(f"- {result.document.title} (分数: {result.document.score:.2f})")
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
