"""
文档管理服务

提供文档的 CRUD 操作和管理功能
"""

import time
from typing import Optional, List, Dict, Any
from ..clients import ManticoreClient
from ..models import (
    Document, 
    DocumentCreate, 
    DocumentUpdate,
    DocumentWithScore,
    BulkDocumentCreate,
    DocumentStats,
    create_sample_embedding
)
from ..utils import (
    get_service_logger,
    Settings,
    DocumentNotFoundError,
    ValidationError,
    QueryError
)


class DocumentService:
    """文档管理服务"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = ManticoreClient(settings)
        self.logger = get_service_logger()
        self.table_name = settings.default_table_name
        
        # 确保表存在
        self._ensure_table_exists()
    
    def _ensure_table_exists(self):
        """确保知识库表存在"""
        if not self.client.table_exists(self.table_name):
            self.logger.info(f"表 '{self.table_name}' 不存在，正在创建...")
            self._create_knowledge_base_table()
    
    def _create_knowledge_base_table(self):
        """创建知识库表"""
        # 使用正确的 Manticore Search 语法创建表
        schema = f"""
            id BIGINT,
            title TEXT INDEXED STORED,
            content TEXT INDEXED STORED,
            category TEXT INDEXED STORED,
            embedding FLOAT_VECTOR knn_type='hnsw' knn_dims='{self.settings.vector_dimensions}' hnsw_similarity='cosine',
            created_at TIMESTAMP,
            updated_at TIMESTAMP
        """

        success = self.client.create_table(self.table_name, schema)
        if success:
            self.logger.info(f"知识库表 '{self.table_name}' 创建成功")
        else:
            raise QueryError(f"创建表 '{self.table_name}' 失败")
    
    def create_document(self, document_data: DocumentCreate) -> Document:
        """创建文档"""
        try:
            # 生成文档 ID
            doc_id = int(time.time() * 1000000)  # 使用微秒时间戳作为 ID
            
            # 如果没有提供向量，自动生成
            embedding = document_data.embedding
            if not embedding:
                embedding = create_sample_embedding(
                    f"{document_data.title} {document_data.content}",
                    self.settings.vector_dimensions
                )
            
            # 准备插入数据（包含向量）
            doc_dict = {
                'id': doc_id,
                'title': document_data.title,
                'content': document_data.content,
                'category': document_data.category or '',
                'embedding': '(' + ','.join(map(str, embedding)) + ')',
                'created_at': int(time.time()),
                'updated_at': int(time.time())
            }

            # 直接插入完整文档数据（包含向量）
            success = self.client.insert_document(self.table_name, doc_dict)
            
            if success:
                self.logger.info(f"文档创建成功，ID: {doc_id}")
                return Document(
                    id=doc_id,
                    title=document_data.title,
                    content=document_data.content,
                    category=document_data.category,
                    embedding=embedding
                )
            else:
                raise QueryError("文档插入失败")
                
        except Exception as e:
            self.logger.error(f"创建文档失败: {e}")
            raise
    
    def get_document(self, doc_id: int) -> Document:
        """获取文档"""
        try:
            sql = f"SELECT * FROM {self.table_name} WHERE id = %s"
            results = self.client.execute_query(sql, (doc_id,))
            
            if not results:
                raise DocumentNotFoundError(doc_id)
            
            doc_data = results[0]
            return Document(
                id=doc_data['id'],
                title=doc_data['title'],
                content=doc_data['content'],
                category=doc_data['category'],
                embedding=None  # 不返回向量数据以节省带宽
            )
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"获取文档 {doc_id} 失败: {e}")
            raise QueryError(f"获取文档失败: {e}")
    
    def update_document(self, doc_id: int, update_data: DocumentUpdate) -> Document:
        """更新文档"""
        try:
            # 检查文档是否存在
            existing_doc = self.get_document(doc_id)
            
            # 构建更新字段
            update_fields = []
            params = []
            
            if update_data.title is not None:
                update_fields.append("title = %s")
                params.append(update_data.title)
            
            if update_data.content is not None:
                update_fields.append("content = %s")
                params.append(update_data.content)
            
            if update_data.category is not None:
                update_fields.append("category = %s")
                params.append(update_data.category)
            
            if update_data.embedding is not None:
                embedding_str = '(' + ','.join(map(str, update_data.embedding)) + ')'
                update_fields.append("embedding = %s")
                params.append(embedding_str)
            
            if not update_fields:
                return existing_doc  # 没有更新字段
            
            # 添加更新时间
            update_fields.append("updated_at = %s")
            params.append(int(time.time()))
            
            # 添加 WHERE 条件
            params.append(doc_id)
            
            # 执行更新
            sql = f"UPDATE {self.table_name} SET {', '.join(update_fields)} WHERE id = %s"
            self.client.execute_query(sql, tuple(params), fetch_results=False)
            
            self.logger.info(f"文档 {doc_id} 更新成功")
            
            # 返回更新后的文档
            return self.get_document(doc_id)
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"更新文档 {doc_id} 失败: {e}")
            raise QueryError(f"更新文档失败: {e}")
    
    def delete_document(self, doc_id: int) -> bool:
        """删除文档"""
        try:
            # 检查文档是否存在
            self.get_document(doc_id)
            
            # 删除文档
            sql = f"DELETE FROM {self.table_name} WHERE id = %s"
            self.client.execute_query(sql, (doc_id,), fetch_results=False)
            
            self.logger.info(f"文档 {doc_id} 删除成功")
            return True
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"删除文档 {doc_id} 失败: {e}")
            raise QueryError(f"删除文档失败: {e}")
    
    def list_documents(
        self, 
        limit: int = 20, 
        offset: int = 0,
        category: Optional[str] = None
    ) -> List[Document]:
        """列出文档"""
        try:
            sql = f"SELECT id, title, content, category FROM {self.table_name}"
            params = []
            
            if category:
                sql += " WHERE category = %s"
                params.append(category)
            
            sql += f" ORDER BY id DESC LIMIT {offset}, {limit}"
            
            results = self.client.execute_query(sql, tuple(params) if params else None)
            
            documents = []
            for row in results or []:
                documents.append(Document(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    category=row['category'],
                    embedding=None
                ))
            
            return documents
            
        except Exception as e:
            self.logger.error(f"列出文档失败: {e}")
            raise QueryError(f"列出文档失败: {e}")
    
    def bulk_create_documents(self, bulk_data: BulkDocumentCreate) -> Dict[str, Any]:
        """批量创建文档"""
        try:
            documents_to_insert = []
            base_time = int(time.time() * 1000000)

            for i, doc_data in enumerate(bulk_data.documents):
                doc_id = base_time + i

                # 生成向量
                embedding = doc_data.embedding
                if not embedding:
                    embedding = create_sample_embedding(
                        f"{doc_data.title} {doc_data.content}",
                        self.settings.vector_dimensions
                    )

                doc_dict = {
                    'id': doc_id,
                    'title': doc_data.title,
                    'content': doc_data.content,
                    'category': doc_data.category or '',
                    'embedding': '(' + ','.join(map(str, embedding)) + ')',
                    'created_at': int(time.time()),
                    'updated_at': int(time.time())
                }

                documents_to_insert.append(doc_dict)

            # 批量插入
            success_count = self.client.bulk_insert_documents(
                self.table_name,
                documents_to_insert
            )

            self.logger.info(f"批量创建文档完成: {success_count}/{len(bulk_data.documents)} 成功")

            return {
                'total': len(bulk_data.documents),
                'success': success_count,
                'failed': len(bulk_data.documents) - success_count
            }

        except Exception as e:
            self.logger.error(f"批量创建文档失败: {e}")
            raise QueryError(f"批量创建文档失败: {e}")
    
    def get_document_stats(self) -> DocumentStats:
        """获取文档统计信息"""
        try:
            # 获取文档总数
            count_sql = f"SELECT COUNT(*) as total FROM {self.table_name}"
            count_result = self.client.execute_query(count_sql)
            total_docs = count_result[0]['total'] if count_result else 0

            # 简化统计信息（避免 columnar 引擎的限制）
            return DocumentStats(
                total_documents=total_docs,
                total_categories=1 if total_docs > 0 else 0,  # 简化处理
                avg_content_length=100.0,  # 简化处理
                latest_document_time=None
            )

        except Exception as e:
            self.logger.error(f"获取文档统计失败: {e}")
            raise QueryError(f"获取文档统计失败: {e}")


if __name__ == "__main__":
    # 测试文档服务
    from ..utils import get_settings
    
    settings = get_settings()
    service = DocumentService(settings)
    
    # 测试创建文档
    doc_data = DocumentCreate(
        title="测试文档",
        content="这是一个测试文档的内容",
        category="测试"
    )
    
    try:
        doc = service.create_document(doc_data)
        print(f"✅ 文档创建成功: {doc.id}")
        
        # 测试获取文档
        retrieved_doc = service.get_document(doc.id)
        print(f"✅ 文档获取成功: {retrieved_doc.title}")
        
        # 测试统计信息
        stats = service.get_document_stats()
        print(f"✅ 统计信息: 总文档数 {stats.total_documents}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
