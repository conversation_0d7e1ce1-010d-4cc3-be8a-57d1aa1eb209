"""
日志管理模块

提供统一的日志配置和管理功能
"""

import logging
import sys
from typing import Optional
from functools import lru_cache
from .config import get_settings


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


@lru_cache()
def setup_logging() -> logging.Logger:
    """设置日志配置"""
    settings = get_settings()
    
    # 创建根日志器
    logger = logging.getLogger("manticore_search")
    logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.log_level.upper()))
    
    # 设置格式化器
    if sys.stdout.isatty():  # 如果是终端，使用彩色格式
        formatter = ColoredFormatter(settings.log_format)
    else:  # 如果是文件或管道，使用普通格式
        formatter = logging.Formatter(settings.log_format)
    
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """获取日志器实例"""
    # 确保根日志器已配置
    setup_logging()
    
    if name:
        return logging.getLogger(f"manticore_search.{name}")
    else:
        return logging.getLogger("manticore_search")


# 预定义的日志器
def get_client_logger() -> logging.Logger:
    """获取客户端日志器"""
    return get_logger("client")


def get_service_logger() -> logging.Logger:
    """获取服务层日志器"""
    return get_logger("service")


def get_api_logger() -> logging.Logger:
    """获取 API 层日志器"""
    return get_logger("api")


# 日志装饰器
def log_function_call(logger: Optional[logging.Logger] = None):
    """记录函数调用的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            _logger = logger or get_logger()
            _logger.debug(f"调用函数: {func.__name__}")
            try:
                result = func(*args, **kwargs)
                _logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                _logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator


def log_async_function_call(logger: Optional[logging.Logger] = None):
    """记录异步函数调用的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            _logger = logger or get_logger()
            _logger.debug(f"调用异步函数: {func.__name__}")
            try:
                result = await func(*args, **kwargs)
                _logger.debug(f"异步函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                _logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试日志功能
    logger = get_logger("test")
    
    logger.debug("这是一条调试信息")
    logger.info("这是一条信息")
    logger.warning("这是一条警告")
    logger.error("这是一条错误")
    logger.critical("这是一条严重错误")
    
    # 测试装饰器
    @log_function_call(logger)
    def test_function():
        return "测试成功"
    
    result = test_function()
    print(f"函数返回: {result}")
