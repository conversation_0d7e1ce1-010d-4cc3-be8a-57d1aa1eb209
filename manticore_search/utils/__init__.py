"""
工具模块

提供配置管理、日志记录、异常处理等通用功能
"""

from .config import Settings, get_settings, create_env_template
from .logger import (
    get_logger, 
    get_client_logger, 
    get_service_logger, 
    get_api_logger,
    log_function_call,
    log_async_function_call
)
from .exceptions import (
    ManticoreSearchError,
    ConnectionError,
    AuthenticationError,
    QueryError,
    ValidationError,
    DocumentNotFoundError,
    TableNotFoundError,
    IndexError,
    SearchError,
    VectorSearchError,
    ConfigurationError,
    TimeoutError,
    ResourceLimitError,
    BatchOperationError,
    handle_pymysql_error
)

__all__ = [
    # 配置
    "Settings",
    "get_settings", 
    "create_env_template",
    
    # 日志
    "get_logger",
    "get_client_logger",
    "get_service_logger", 
    "get_api_logger",
    "log_function_call",
    "log_async_function_call",
    
    # 异常
    "ManticoreSearchError",
    "ConnectionError",
    "AuthenticationError", 
    "QueryError",
    "ValidationError",
    "DocumentNotFoundError",
    "TableNotFoundError",
    "IndexError",
    "SearchError",
    "VectorSearchError",
    "ConfigurationError",
    "TimeoutError",
    "ResourceLimitError",
    "BatchOperationError",
    "handle_pymysql_error",
]
