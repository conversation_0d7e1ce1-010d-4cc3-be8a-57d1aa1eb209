"""
异常定义模块

定义 Manticore Search 模块的所有自定义异常
"""

from typing import Optional, Any, Dict


class ManticoreSearchError(Exception):
    """Manticore Search 模块基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class ConnectionError(ManticoreSearchError):
    """连接相关异常"""
    
    def __init__(self, message: str = "无法连接到 Manticore Search", **kwargs):
        super().__init__(message, error_code="CONNECTION_ERROR", **kwargs)


class AuthenticationError(ManticoreSearchError):
    """认证相关异常"""
    
    def __init__(self, message: str = "Manticore Search 认证失败", **kwargs):
        super().__init__(message, error_code="AUTH_ERROR", **kwargs)


class QueryError(ManticoreSearchError):
    """查询相关异常"""
    
    def __init__(self, message: str = "查询执行失败", **kwargs):
        super().__init__(message, error_code="QUERY_ERROR", **kwargs)


class ValidationError(ManticoreSearchError):
    """数据验证异常"""
    
    def __init__(self, message: str = "数据验证失败", **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)


class DocumentNotFoundError(ManticoreSearchError):
    """文档未找到异常"""
    
    def __init__(self, doc_id: Any, **kwargs):
        message = f"文档 ID {doc_id} 未找到"
        super().__init__(message, error_code="DOCUMENT_NOT_FOUND", **kwargs)
        self.details["doc_id"] = doc_id


class TableNotFoundError(ManticoreSearchError):
    """表未找到异常"""
    
    def __init__(self, table_name: str, **kwargs):
        message = f"表 '{table_name}' 不存在"
        super().__init__(message, error_code="TABLE_NOT_FOUND", **kwargs)
        self.details["table_name"] = table_name


class IndexError(ManticoreSearchError):
    """索引相关异常"""
    
    def __init__(self, message: str = "索引操作失败", **kwargs):
        super().__init__(message, error_code="INDEX_ERROR", **kwargs)


class SearchError(ManticoreSearchError):
    """搜索相关异常"""
    
    def __init__(self, message: str = "搜索操作失败", **kwargs):
        super().__init__(message, error_code="SEARCH_ERROR", **kwargs)


class VectorSearchError(SearchError):
    """向量搜索异常"""
    
    def __init__(self, message: str = "向量搜索失败", **kwargs):
        super().__init__(message, error_code="VECTOR_SEARCH_ERROR", **kwargs)


class ConfigurationError(ManticoreSearchError):
    """配置相关异常"""
    
    def __init__(self, message: str = "配置错误", **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)


class TimeoutError(ManticoreSearchError):
    """超时异常"""
    
    def __init__(self, operation: str = "操作", timeout: int = 30, **kwargs):
        message = f"{operation}超时 ({timeout}秒)"
        super().__init__(message, error_code="TIMEOUT_ERROR", **kwargs)
        self.details.update({"operation": operation, "timeout": timeout})


class ResourceLimitError(ManticoreSearchError):
    """资源限制异常"""
    
    def __init__(self, resource: str, limit: Any, current: Any, **kwargs):
        message = f"{resource}超出限制: 当前值 {current}, 限制值 {limit}"
        super().__init__(message, error_code="RESOURCE_LIMIT_ERROR", **kwargs)
        self.details.update({
            "resource": resource,
            "limit": limit,
            "current": current
        })


class BatchOperationError(ManticoreSearchError):
    """批量操作异常"""
    
    def __init__(self, message: str = "批量操作失败", failed_items: Optional[list] = None, **kwargs):
        super().__init__(message, error_code="BATCH_OPERATION_ERROR", **kwargs)
        self.details["failed_items"] = failed_items or []


# 异常处理工具函数
def handle_pymysql_error(error: Exception) -> ManticoreSearchError:
    """处理 PyMySQL 异常，转换为自定义异常"""
    import pymysql
    
    if isinstance(error, pymysql.err.OperationalError):
        if "Can't connect" in str(error):
            return ConnectionError(f"连接失败: {error}")
        elif "timeout" in str(error).lower():
            return TimeoutError("数据库连接", 30)
        else:
            return QueryError(f"操作错误: {error}")
    
    elif isinstance(error, pymysql.err.ProgrammingError):
        return QueryError(f"SQL 语法错误: {error}")
    
    elif isinstance(error, pymysql.err.IntegrityError):
        return ValidationError(f"数据完整性错误: {error}")
    
    elif isinstance(error, pymysql.err.DataError):
        return ValidationError(f"数据错误: {error}")
    
    else:
        return ManticoreSearchError(f"未知数据库错误: {error}")


if __name__ == "__main__":
    # 测试异常
    try:
        raise DocumentNotFoundError(123, details={"table": "test_table"})
    except ManticoreSearchError as e:
        print("捕获异常:")
        print(f"类型: {type(e).__name__}")
        print(f"消息: {e.message}")
        print(f"错误码: {e.error_code}")
        print(f"详情: {e.details}")
        print(f"字典格式: {e.to_dict()}")
