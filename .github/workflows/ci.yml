name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      manticore:
        image: manticoresearch/manticore:latest
        ports:
          - 9308:9308
          - 9306:9306
      otel-collector:
        image: otel/opentelemetry-collector:latest
        volumes:
          - ./examples/otel-collector/otel-collector-config.yml:/etc/otel-collector-config.yml
        # Rely on default command; config provided via mounted file
        ports:
          - 4318:4318
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        env:
          OTEL_EXPORTER_OTLP_ENDPOINT: http://localhost:4318/v1/traces
        run: npm test --silent