#!/usr/bin/env python3
"""
Manticore Search Demo Script
演示 Manticore Search 的核心功能：CRUD、全文搜索、向量搜索、摘要存储检索
"""

import pymysql
import json
import numpy as np
import time
import sys
from typing import List, Dict, Any, Optional

class ManticoreDemo:
    def __init__(self, host='localhost', port=9306, user='', password=''):
        """初始化 Manticore Search 连接"""
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.connection = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                charset='utf8mb4',
                autocommit=True
            )
            print(f"✅ 成功连接到 Manticore Search ({self.host}:{self.port})")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def execute_sql(self, sql: str, params=None) -> Optional[List[Dict]]:
        """执行SQL语句并返回结果"""
        if not self.connection:
            print("❌ 数据库未连接")
            return None
            
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                print(f"🔍 执行SQL: {sql}")
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                # 获取结果
                if sql.strip().upper().startswith('SELECT') or sql.strip().upper().startswith('SHOW'):
                    results = cursor.fetchall()
                    print(f"📊 返回 {len(results)} 条记录")
                    return results
                else:
                    print("✅ SQL执行成功")
                    return []
        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return None
    
    def create_demo_table(self):
        """创建演示用的表"""
        print("\n🏗️  创建演示表...")
        
        # 删除已存在的表
        drop_sql = "DROP TABLE IF EXISTS knowledge_base"
        self.execute_sql(drop_sql)
        
        # 创建包含文本字段和向量字段的表
        create_sql = """
        CREATE TABLE knowledge_base (
            id BIGINT,
            title TEXT INDEXED STORED,
            content TEXT INDEXED STORED,
            category TEXT INDEXED STORED,
            embedding FLOAT_VECTOR knn_type='hnsw' knn_dims='128' hnsw_similarity='cosine',
            created_at TIMESTAMP
        ) engine='columnar'
        """
        
        result = self.execute_sql(create_sql)
        if result is not None:
            print("✅ 表 'knowledge_base' 创建成功")
            return True
        return False
    
    def generate_sample_embedding(self, text: str) -> List[float]:
        """生成示例向量（实际应用中应使用真实的embedding模型）"""
        # 简单的文本哈希向量化（仅用于演示）
        np.random.seed(hash(text) % (2**32))
        embedding = np.random.normal(0, 1, 128).tolist()
        # 归一化
        norm = np.linalg.norm(embedding)
        return [x / norm for x in embedding]
    
    def insert_sample_data(self):
        """插入示例数据"""
        print("\n📝 插入示例数据...")
        
        sample_data = [
            {
                'id': 1,
                'title': 'Manticore Search 介绍',
                'content': 'Manticore Search 是一个强大的全文搜索引擎，支持SQL查询、全文搜索和向量搜索功能。它可以处理大量文档并提供快速的搜索响应。',
                'category': '技术文档'
            },
            {
                'id': 2,
                'title': '向量搜索原理',
                'content': 'KNN（K-最近邻）搜索是一种基于向量相似度的搜索方法。通过计算查询向量与数据库中向量的距离，找到最相似的K个结果。',
                'category': '算法原理'
            },
            {
                'id': 3,
                'title': 'Python 客户端使用',
                'content': 'Manticore Search 提供了多种客户端接口，包括MySQL协议、HTTP JSON API等。Python可以通过pymysql等库连接使用。',
                'category': '开发指南'
            },
            {
                'id': 4,
                'title': '实时索引特性',
                'content': 'Manticore Search 支持实时索引，可以即时插入、更新和删除文档，无需重建整个索引。这使得它非常适合动态内容管理。',
                'category': '技术特性'
            },
            {
                'id': 5,
                'title': '分布式搜索',
                'content': 'Manticore Search 支持分布式部署，可以将数据分片存储在多个节点上，提供高可用性和水平扩展能力。',
                'category': '架构设计'
            }
        ]
        
        for data in sample_data:
            # 生成向量
            embedding = self.generate_sample_embedding(data['content'])
            embedding_str = '(' + ','.join(map(str, embedding)) + ')'

            insert_sql = f"""
            INSERT INTO knowledge_base (id, title, content, category, embedding, created_at)
            VALUES ({data['id']}, %s, %s, %s, {embedding_str}, NOW())
            """

            result = self.execute_sql(insert_sql, (data['title'], data['content'], data['category']))
            if result is not None:
                print(f"✅ 插入记录: {data['title']}")
            else:
                print(f"❌ 插入失败: {data['title']}")
                return False
        
        print("✅ 所有示例数据插入完成")
        return True
    
    def demo_fulltext_search(self):
        """演示全文搜索功能"""
        print("\n🔍 演示全文搜索功能...")
        
        search_queries = [
            "搜索引擎",
            "Python 客户端",
            "向量搜索",
            "实时索引"
        ]
        
        for query in search_queries:
            print(f"\n🔎 搜索关键词: '{query}'")
            
            search_sql = f"""
            SELECT id, title, content, category, 
                   WEIGHT() as relevance_score
            FROM knowledge_base 
            WHERE MATCH('{query}')
            ORDER BY relevance_score DESC
            LIMIT 3
            """
            
            results = self.execute_sql(search_sql)
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. [{result['category']}] {result['title']}")
                    print(f"     相关度: {result['relevance_score']}")
                    print(f"     内容: {result['content'][:100]}...")
            else:
                print("  未找到相关结果")
    
    def demo_vector_search(self):
        """演示向量搜索功能"""
        print("\n🎯 演示向量搜索功能...")
        
        # 生成查询向量
        query_text = "机器学习和人工智能搜索算法"
        query_vector = self.generate_sample_embedding(query_text)
        vector_str = '(' + ','.join(map(str, query_vector)) + ')'

        print(f"🔎 查询向量基于文本: '{query_text}'")

        knn_sql = f"""
        SELECT id, title, content, category,
               DOT(embedding, {vector_str}) as similarity_score
        FROM knowledge_base
        ORDER BY similarity_score DESC
        LIMIT 3
        """
        
        results = self.execute_sql(knn_sql)
        if results:
            print("📊 向量相似度搜索结果:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. [{result['category']}] {result['title']}")
                print(f"     相似度: {result['similarity_score']:.4f}")
                print(f"     内容: {result['content'][:100]}...")
        else:
            print("❌ 向量搜索失败")
    
    def demo_hybrid_search(self):
        """演示混合搜索（全文+向量）"""
        print("\n🔄 演示混合搜索功能...")
        
        query_text = "搜索技术"
        query_vector = self.generate_sample_embedding(query_text)
        vector_str = '(' + ','.join(map(str, query_vector)) + ')'
        
        print(f"🔎 混合搜索 - 关键词: '{query_text}' + 向量相似度")
        
        hybrid_sql = f"""
        SELECT id, title, content, category,
               WEIGHT() as text_score,
               DOT(embedding, {vector_str}) as vector_score,
               (WEIGHT() * 0.7 + DOT(embedding, {vector_str}) * 0.3) as combined_score
        FROM knowledge_base
        WHERE MATCH('{query_text}')
        ORDER BY combined_score DESC
        LIMIT 3
        """
        
        results = self.execute_sql(hybrid_sql)
        if results:
            print("📊 混合搜索结果:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. [{result['category']}] {result['title']}")
                print(f"     文本得分: {result['text_score']:.4f}")
                print(f"     向量得分: {result['vector_score']:.4f}")
                print(f"     综合得分: {result['combined_score']:.4f}")
        else:
            print("❌ 混合搜索失败")
    
    def demo_crud_operations(self):
        """演示CRUD操作"""
        print("\n⚙️  演示CRUD操作...")
        
        # CREATE - 插入新记录
        print("📝 CREATE - 插入新记录")
        new_embedding = self.generate_sample_embedding("新的测试文档内容")
        embedding_str = '(' + ','.join(map(str, new_embedding)) + ')'
        
        insert_sql = f"""
        INSERT INTO knowledge_base (id, title, content, category, embedding, created_at)
        VALUES (999, %s, %s, %s, {embedding_str}, NOW())
        """
        
        self.execute_sql(insert_sql, ("测试文档", "这是一个用于测试CRUD操作的文档", "测试"))
        
        # READ - 查询记录
        print("\n📖 READ - 查询记录")
        select_sql = "SELECT id, title, category FROM knowledge_base WHERE id = 999"
        results = self.execute_sql(select_sql)
        if results:
            print(f"  找到记录: {results[0]}")
        
        # UPDATE - 更新记录
        print("\n✏️  UPDATE - 更新记录")
        update_sql = "UPDATE knowledge_base SET title = %s WHERE id = 999"
        self.execute_sql(update_sql, ("更新后的测试文档",))
        
        # 验证更新
        results = self.execute_sql(select_sql)
        if results:
            print(f"  更新后记录: {results[0]}")
        
        # DELETE - 删除记录
        print("\n🗑️  DELETE - 删除记录")
        delete_sql = "DELETE FROM knowledge_base WHERE id = 999"
        self.execute_sql(delete_sql)
        
        # 验证删除
        results = self.execute_sql(select_sql)
        if not results:
            print("  ✅ 记录已成功删除")
    
    def demo_snippets(self):
        """演示摘要生成功能"""
        print("\n📄 演示摘要生成功能...")
        
        # 使用HIGHLIGHT函数生成高亮摘要
        query = "搜索引擎"
        highlight_sql = f"""
        SELECT id, title, 
               HIGHLIGHT({{limit=100, before_match='<b>', after_match='</b>'}}, content, '{query}') as snippet
        FROM knowledge_base
        WHERE MATCH('{query}')
        LIMIT 2
        """
        
        results = self.execute_sql(highlight_sql)
        if results:
            print(f"🔎 关键词 '{query}' 的高亮摘要:")
            for result in results:
                print(f"  📌 {result['title']}")
                print(f"     {result['snippet']}")
        else:
            print("❌ 摘要生成失败")
    
    def show_table_info(self):
        """显示表信息"""
        print("\n📊 表结构信息...")
        
        # 显示表结构
        desc_sql = "DESCRIBE knowledge_base"
        results = self.execute_sql(desc_sql)
        if results:
            print("表结构:")
            for field in results:
                print(f"  {field}")
        
        # 显示记录数
        count_sql = "SELECT COUNT(*) as total FROM knowledge_base"
        results = self.execute_sql(count_sql)
        if results:
            print(f"\n总记录数: {results[0]['total']}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🚀 Manticore Search Demo 开始")
    print("=" * 50)
    
    # 创建demo实例
    demo = ManticoreDemo()
    
    # 连接数据库
    if not demo.connect():
        print("❌ 无法连接到Manticore Search，请确保服务正在运行")
        sys.exit(1)
    
    try:
        # 执行演示步骤
        if demo.create_demo_table():
            demo.insert_sample_data()
            demo.show_table_info()
            demo.demo_fulltext_search()
            demo.demo_vector_search()
            demo.demo_hybrid_search()
            demo.demo_crud_operations()
            demo.demo_snippets()
        
        print("\n🎉 Demo 执行完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo 被用户中断")
    except Exception as e:
        print(f"\n❌ Demo 执行出错: {e}")
    finally:
        demo.close()

if __name__ == "__main__":
    main()
