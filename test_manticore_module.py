#!/usr/bin/env python3
"""
Manticore Search 模块测试脚本

测试模块的各项功能
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from manticore_search import (
    ManticoreClient,
    DocumentService,
    SearchService,
    HealthService,
    DocumentCreate,
    create_simple_search_request,
    get_settings,
    get_logger
)


async def test_basic_functionality():
    """测试基本功能"""
    logger = get_logger("test")
    settings = get_settings()
    
    logger.info("🧪 开始测试 Manticore Search 模块...")
    
    try:
        # 1. 测试客户端连接
        logger.info("1️⃣ 测试客户端连接...")
        client = ManticoreClient(settings)
        if client.test_connection():
            logger.info("✅ 客户端连接成功")
        else:
            logger.error("❌ 客户端连接失败")
            return False
        
        # 2. 测试健康检查服务
        logger.info("2️⃣ 测试健康检查服务...")
        health_service = HealthService(settings)
        health_response = await health_service.get_health_status()
        logger.info(f"✅ 健康检查完成，状态: {health_response.status}")
        
        # 3. 测试文档服务
        logger.info("3️⃣ 测试文档服务...")
        document_service = DocumentService(settings)
        
        # 创建测试文档
        test_doc = DocumentCreate(
            title="模块测试文档",
            content="这是一个用于测试 Manticore Search 模块功能的文档。包含了各种测试内容和关键词。",
            category="模块测试"
        )
        
        created_doc = document_service.create_document(test_doc)
        logger.info(f"✅ 文档创建成功，ID: {created_doc.id}")
        
        # 获取文档
        retrieved_doc = document_service.get_document(created_doc.id)
        logger.info(f"✅ 文档获取成功: {retrieved_doc.title}")
        
        # 4. 测试搜索服务
        logger.info("4️⃣ 测试搜索服务...")
        search_service = SearchService(settings)
        
        # 全文搜索
        search_request = create_simple_search_request("测试", 5)
        search_response = search_service.search(search_request)
        logger.info(f"✅ 搜索完成，找到 {search_response.total} 个结果，耗时 {search_response.took:.2f}ms")
        
        # 显示搜索结果
        for i, result in enumerate(search_response.results[:3], 1):
            logger.info(f"   {i}. {result.document.title} (分数: {result.document.score:.2f})")
        
        # 5. 测试统计功能
        logger.info("5️⃣ 测试统计功能...")
        stats = document_service.get_document_stats()
        logger.info(f"✅ 统计信息获取成功:")
        logger.info(f"   - 总文档数: {stats.total_documents}")
        logger.info(f"   - 总分类数: {stats.total_categories}")
        logger.info(f"   - 平均内容长度: {stats.avg_content_length:.1f}")
        
        # 6. 清理测试数据
        logger.info("6️⃣ 清理测试数据...")
        document_service.delete_document(created_doc.id)
        logger.info("✅ 测试数据清理完成")
        
        logger.info("🎉 所有测试通过！模块功能正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration():
    """测试配置功能"""
    logger = get_logger("config_test")
    
    logger.info("🔧 测试配置功能...")
    
    try:
        settings = get_settings()
        
        logger.info("配置信息:")
        logger.info(f"  - Manticore 主机: {settings.manticore_host}")
        logger.info(f"  - Manticore 端口: {settings.manticore_port}")
        logger.info(f"  - 默认表名: {settings.default_table_name}")
        logger.info(f"  - 向量维度: {settings.vector_dimensions}")
        logger.info(f"  - 日志级别: {settings.log_level}")
        
        logger.info("✅ 配置加载成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Manticore Search 模块测试")
    print("=" * 50)
    
    # 测试配置
    config_ok = test_configuration()
    if not config_ok:
        print("❌ 配置测试失败，退出")
        sys.exit(1)
    
    # 测试基本功能
    try:
        functionality_ok = asyncio.run(test_basic_functionality())
        if functionality_ok:
            print("\n🎉 所有测试通过！")
            print("模块已准备就绪，可以启动 API 服务")
        else:
            print("\n❌ 功能测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
