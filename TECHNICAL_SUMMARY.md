# Manticore Search 技术总结与模块化指南

## 1. 技术发现和经验总结

### 1.1 DeepWiki 调研关键发现

**核心技术特性：**
- Manticore Search 13.6.7 版本稳定可用
- 支持 MySQL 协议（端口 9306）和 HTTP JSON API（端口 9308）
- 原生支持全文搜索、向量搜索（FLOAT_VECTOR）和混合搜索
- 实时索引，支持即时 CRUD 操作

**连接和部署：**
- Docker 部署最为稳定，官方镜像 `manticoresearch/manticore:latest`
- Python 客户端推荐使用 `pymysql`（MySQL 协议）
- 配置文件支持中文分词：`morphology='icu_chinese'`

### 1.2 实际测试验证结果

**✅ 完全验证的功能：**
```python
# 基础连接
connection = pymysql.connect(host='localhost', port=9306, charset='utf8mb4')

# 表创建（支持中文分词）
CREATE TABLE knowledge_base (
    id BIGINT,
    title TEXT INDEXED STORED,
    content TEXT INDEXED STORED,
    category STRING INDEXED STORED,
    created_at TIMESTAMP
) morphology='icu_chinese'

# 全文搜索
SELECT id, title, WEIGHT() as score 
FROM knowledge_base 
WHERE MATCH('Python') 
ORDER BY score DESC

# 高亮摘要
SELECT HIGHLIGHT({limit=100, before_match='<b>', after_match='</b>'}, content, 'keyword') 
FROM knowledge_base WHERE MATCH('keyword')
```

**❌ 发现的技术限制：**

1. **TEXT 字段更新限制**
   ```python
   # ❌ 直接 UPDATE 不支持
   UPDATE knowledge_base SET title = 'new_title' WHERE id = 1
   
   # ✅ 必须使用 REPLACE
   REPLACE INTO knowledge_base (id, title, content, category, created_at)
   VALUES (1, 'new_title', 'content', 'category', timestamp)
   ```

2. **向量搜索语法问题**
   ```python
   # ❌ 测试失败的语法
   SELECT * FROM table ORDER BY KNN(embedding, (0.1,0.2,0.3)) ASC
   SELECT * FROM table WHERE DOT(embedding, (0.1,0.2,0.3)) > 0.5
   
   # ⚠️ 需要进一步研究正确语法
   ```

3. **中文分词效果**
   - 英文关键词搜索完全正常
   - 中文关键词部分生效，需要优化分词配置

### 1.3 语法陷阱和最佳实践

**时间戳处理：**
```python
# ❌ NOW() 函数不可用
INSERT INTO table (created_at) VALUES (NOW())

# ✅ 使用 Unix 时间戳
import time
current_time = int(time.time())
INSERT INTO table (created_at) VALUES (%s)
```

**参数化查询：**
```python
# ✅ 推荐方式
cursor.execute("SELECT * FROM table WHERE MATCH(%s)", (query,))

# ❌ 避免字符串拼接
sql = f"SELECT * FROM table WHERE MATCH('{query}')"  # SQL注入风险
```

**连接管理：**
```python
# ✅ 使用连接池和自动提交
connection = pymysql.connect(
    host='localhost', port=9306,
    charset='utf8mb4', autocommit=True
)
```

## 2. 当前 Demo 架构分析

### 2.1 文件结构和功能模块

```
zhi-manticore2/
├── test_manticore.py      # 快速连接测试
├── simple_demo.py         # 基础功能演示
├── final_demo.py          # 完整功能演示
├── demo_summary.py        # 功能验证总结
├── docker-compose.yml     # 容器配置
├── manticore.conf         # Manticore 配置
├── requirements.txt       # Python 依赖
├── run_demo.sh           # 一键运行脚本
└── README.md             # 使用文档
```

### 2.2 已验证功能的技术实现

**连接管理模式：**
```python
class ManticoreDemo:
    def __init__(self, host='localhost', port=9306):
        self.connection = None
    
    def connect(self):
        self.connection = pymysql.connect(
            host=self.host, port=self.port,
            charset='utf8mb4', autocommit=True
        )
    
    def execute_sql(self, sql, params=None):
        with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(sql, params)
            return cursor.fetchall()
```

**数据模型设计：**
```sql
-- 核心表结构
CREATE TABLE knowledge_base (
    id BIGINT,                    -- 文档ID
    title TEXT INDEXED STORED,    -- 标题（全文索引+存储）
    content TEXT INDEXED STORED,  -- 内容（全文索引+存储）
    category STRING INDEXED STORED, -- 分类（索引+存储）
    created_at TIMESTAMP          -- 创建时间
) morphology='icu_chinese'        -- 中文分词配置
```

**错误处理机制：**
```python
def execute_sql(self, sql, params=None):
    try:
        cursor.execute(sql, params)
        return cursor.fetchall()
    except Exception as e:
        print(f"❌ SQL执行失败: {e}")
        return None
```

### 2.3 性能和稳定性考虑

**连接稳定性：**
- 单连接模式在演示中表现稳定
- 生产环境需要连接池管理
- 需要实现连接重试机制

**查询性能：**
- 全文搜索响应速度良好（毫秒级）
- WEIGHT() 函数提供相关度评分
- LIMIT 和 OFFSET 支持分页

## 3. 技术债务和改进点

### 3.1 当前实现局限性

1. **单连接无池化管理**
2. **错误处理过于简单**
3. **缺乏日志记录机制**
4. **没有配置管理系统**
5. **缺乏性能监控**

### 3.2 需要优化的功能点

**中文分词优化：**
```sql
-- 当前配置
morphology='icu_chinese'

-- 可能需要的额外配置
charset_table = '0..9, A..Z->a..z, _, a..z, U+4E00..U+9FFF'
```

**向量搜索实现：**
- 研究正确的 FLOAT_VECTOR 语法
- 实现备用的相似度计算方案
- 考虑外部向量化模型集成

**性能优化：**
- 实现查询缓存
- 添加索引优化建议
- 批量操作优化

## 4. 下一阶段模块化方案

### 4.1 FastAPI 架构设计思路

**分层架构：**
```
manticore_api/
├── __init__.py           # 模块入口
├── main.py              # FastAPI 应用入口
├── models/              # 数据模型
│   ├── __init__.py
│   ├── request.py       # 请求模型
│   ├── response.py      # 响应模型
│   └── document.py      # 文档模型
├── services/            # 业务逻辑层
│   ├── __init__.py
│   ├── search.py        # 搜索服务
│   ├── document.py      # 文档管理服务
│   └── health.py        # 健康检查服务
├── clients/             # 数据访问层
│   ├── __init__.py
│   ├── manticore.py     # Manticore 客户端
│   └── connection.py    # 连接管理
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── config.py        # 配置管理
│   ├── logger.py        # 日志管理
│   └── exceptions.py    # 异常定义
└── tests/               # 测试模块
    ├── __init__.py
    ├── test_search.py
    ├── test_document.py
    └── conftest.py
```

### 4.2 模块划分和接口设计原则

**核心接口设计：**
```python
# 搜索接口
@app.post("/api/v1/search")
async def search_documents(request: SearchRequest) -> SearchResponse

# 文档管理接口
@app.post("/api/v1/documents")
async def create_document(document: Document) -> ApiResponse

@app.get("/api/v1/documents/{doc_id}")
async def get_document(doc_id: int) -> Document

@app.put("/api/v1/documents/{doc_id}")
async def update_document(doc_id: int, request: UpdateRequest) -> ApiResponse

@app.delete("/api/v1/documents/{doc_id}")
async def delete_document(doc_id: int) -> ApiResponse

# 批量操作接口
@app.post("/api/v1/documents/bulk")
async def bulk_insert(request: BulkInsertRequest) -> ApiResponse

# 健康检查接口
@app.get("/api/v1/health")
async def health_check() -> HealthCheck
```

### 4.3 数据流和错误处理策略

**数据流设计：**
```
Request → Validation → Service Layer → Client Layer → Manticore → Response
```

**错误处理策略：**
```python
# 分层异常处理
class ManticoreAPIException(Exception):
    def __init__(self, message: str, code: str = None, status_code: int = 500):
        self.message = message
        self.code = code
        self.status_code = status_code

# 全局异常处理器
@app.exception_handler(ManticoreAPIException)
async def manticore_exception_handler(request: Request, exc: ManticoreAPIException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"success": False, "message": exc.message, "error_code": exc.code}
    )
```

### 4.4 测试和部署考虑

**测试策略：**
```python
# 单元测试
pytest tests/test_search.py -v

# 集成测试
pytest tests/test_integration.py -v

# 性能测试
pytest tests/test_performance.py -v
```

**部署配置：**
```yaml
# docker-compose.yml 扩展
services:
  manticore-api:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - manticore
    environment:
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306
```

## 5. 实施建议

### 5.1 开发优先级

1. **Phase 1**: 核心客户端和连接管理
2. **Phase 2**: 基础 CRUD API 实现
3. **Phase 3**: 搜索功能和高亮摘要
4. **Phase 4**: 批量操作和性能优化
5. **Phase 5**: 向量搜索和高级功能

### 5.2 质量保证

- 每个模块都要有对应的测试脚本
- 使用 pytest 进行自动化测试
- 集成 CI/CD 流程
- 性能基准测试和监控

这个技术总结为后续的模块化开发提供了坚实的基础和明确的指导方向。
